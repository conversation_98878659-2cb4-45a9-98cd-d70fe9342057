-- <PERSON><PERSON><PERSON><PERSON><PERSON> sloupce Icon, pokud neexistuje
IF NOT EXISTS (SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID(N'[dbo].[Categories]') AND name = 'Icon')
BEGIN
    ALTER TABLE Categories
    ADD Icon VARCHAR(255);
END

-- <PERSON><PERSON><PERSON><PERSON><PERSON> sloupce OrderIndex, pokud neexistuje
IF NOT EXISTS (SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID(N'[dbo].[Categories]') AND name = 'OrderIndex')
BEGIN
    ALTER TABLE Categories
    ADD OrderIndex INT;
END

-- Nastavení výchozí hodnoty OrderIndex pro existující záznamy
-- Nastavíme OrderIndex podle ID kategorie, aby byl každ<PERSON> z<PERSON>znam unikátní
UPDATE Categories 
SET OrderIndex = CategoryID 
WHERE OrderIndex IS NULL;

-- Nastavení NOT NULL constraint pro OrderIndex
ALTER TABLE Categories
ALTER COLUMN OrderIndex INT NOT NULL;

-- Vytvoření indexu pro rychlejší řazení, pokud neexistuje
IF NOT EXISTS (SELECT * FROM sys.indexes 
    WHERE name = 'IX_Categories_OrderIndex' AND object_id = OBJECT_ID('Categories'))
BEGIN
    CREATE INDEX IX_Categories_OrderIndex ON Categories(OrderIndex);
END

-- Přidání komentářů pro dokumentaci
IF NOT EXISTS (SELECT * FROM fn_listextendedproperty(N'MS_Description', 'SCHEMA', N'dbo', 'TABLE', N'Categories', 'COLUMN', N'Icon'))
BEGIN
    EXEC sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'Cesta k ikoně kategorie',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'Categories',
        @level2type = N'COLUMN',
        @level2name = N'Icon';
END

IF NOT EXISTS (SELECT * FROM fn_listextendedproperty(N'MS_Description', 'SCHEMA', N'dbo', 'TABLE', N'Categories', 'COLUMN', N'OrderIndex'))
BEGIN
    EXEC sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'Index pro řazení kategorií',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'Categories',
        @level2type = N'COLUMN',
        @level2name = N'OrderIndex';
END

GO

-- Aktualizace existujících dat v sample_data.sql
UPDATE Categories
SET Icon = 'obchodni-zbozi.png'
WHERE Name = N'Obchodní zboží';

UPDATE Categories
SET Icon = 'spojovaci-material.png'
WHERE Name = N'Nerezový spojovací materiál';

UPDATE Categories
SET Icon = 'srouby.png'
WHERE Name = N'Šrouby';

UPDATE Categories
SET Icon = 'srouby-plech.png'
WHERE Name = N'Šrouby do plechu';

UPDATE Categories
SET Icon = 'srouby-plech-al.png'
WHERE Name = N'Šrouby do plechu Al';

UPDATE Categories
SET Icon = 'din7982.png'
WHERE Name = N'DIN 7982';

UPDATE Categories
SET Icon = 'din7982c.png'
WHERE Name = N'DIN 7982 C';

-- Nastavení OrderIndex pro hlavní kategorie
UPDATE Categories
SET OrderIndex = 10
WHERE Name = N'Obchodní zboží';

UPDATE Categories
SET OrderIndex = 20
WHERE Name = N'Nerezový spojovací materiál';

UPDATE Categories
SET OrderIndex = 30
WHERE Name = N'Šrouby';

-- Ostatní kategorie ponecháme s výchozím OrderIndex


Microsoft OLE DB Provider for SQL Server error '80040e14'

Incorrect syntax near the keyword 'UNION'.

/includes/database.asp, line 30


Microsoft OLE DB Provider for SQL Server error '80040e14'

Incorrect syntax near the keyword 'UNION'.

/includes/database.asp, line 30


http://localhost:82/catalog.asp?category=vruty toto je ok a http://localhost:82/catalog.asp toto ne

ADODB.Recordset error '800a0cc1'

Item cannot be found in the collection corresponding to the requested name or ordinal.

/catalog.asp, line 164