<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<!--#include file="includes/security.asp"-->
<%
Dim errorMessage
Dim errors
Set errors = CreateObject("Scripting.Dictionary") ' Initialize errors Dictionary outside the POST check

If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim email, password, firstName, lastName, phone, sql
    
    email = Trim(Request.Form("email"))
    password = Trim(Request.Form("password"))
    firstName = Trim(Request.Form("firstName"))
    lastName = Trim(Request.Form("lastName"))
    phone = Trim(Request.Form("phone"))
    
    ' Validace
    If email = "" Then
        errors.Add "email", "Email je povinný"
    ElseIf Not ValidateEmail(email) Then
        errors.Add "email", "Neplatný formát emailu"
    ElseIf GetSingleValue("SELECT COUNT(*) FROM Users WHERE Email = " & SafeSQL(email)) > 0 Then
        errors.Add "email", "Email již existuje"
    End If
    
    If password = "" Then
        errors.Add "password", "Heslo je povinné"
    ElseIf Len(password) < 6 Then
        errors.Add "password", "Heslo musí mít alespoň 6 znaků"
    End If
    
    If firstName = "" Then errors.Add "firstName", "Jméno je povinné"
    If lastName = "" Then errors.Add "lastName", "Příjmení je povinné"
    
    If errors.Count = 0 Then
        Dim hashedPassword: hashedPassword = HashPassword(password)
        
        sql = "INSERT INTO Users (Email, Password, FirstName, LastName, Phone, Created) VALUES (" & _
              SafeSQL(email) & ", " & _
              SafeSQL(hashedPassword) & ", " & _
              SafeSQL(firstName) & ", " & _
              SafeSQL(lastName) & ", " & _
              SafeSQL(phone) & ", " & _
              "GETDATE())"
              
        ExecuteSQL sql
        Response.Redirect "login.asp?registered=1"
    End If
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Registrace - <%=SITE_NAME%></title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!--#include file="includes/header.asp"-->
    
    <div class="container">
        <div class="auth-form">
            <h1>Registrace</h1>
            
            <form method="post" action="register.asp">
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" value="<%=Request.Form("email")%>" required>
                    <% If errors.Exists("email") Then %>
                        <span class="error-message"><%=errors("email")%></span>
                    <% End If %>
                </div>
                
                <div class="form-group">
                    <label for="password">Heslo:</label>
                    <input type="password" id="password" name="password" required>
                    <% If errors.Exists("password") Then %>
                        <span class="error-message"><%=errors("password")%></span>
                    <% End If %>
                </div>
                
                <div class="form-group">
                    <label for="firstName">Jméno:</label>
                    <input type="text" id="firstName" name="firstName" value="<%=Request.Form("firstName")%>" required>
                    <% If errors.Exists("firstName") Then %>
                        <span class="error-message"><%=errors("firstName")%></span>
                    <% End If %>
                </div>
                
                <div class="form-group">
                    <label for="lastName">Příjmení:</label>
                    <input type="text" id="lastName" name="lastName" value="<%=Request.Form("lastName")%>" required>
                    <% If errors.Exists("lastName") Then %>
                        <span class="error-message"><%=errors("lastName")%></span>
                    <% End If %>
                </div>
                
                <div class="form-group">
                    <label for="phone">Telefon:</label>
                    <input type="tel" id="phone" name="phone" value="<%=Request.Form("phone")%>">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Registrovat se</button>
                    <a href="login.asp" class="btn btn-link">Přihlášení</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>


