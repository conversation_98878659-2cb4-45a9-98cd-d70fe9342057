<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/session.asp"-->
<%
CheckSession()

If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim userId: userId = Session("UserID")
    
    ' Načtení dat z POST requestu
    Dim data: data = Request.Form()
    Dim cartName: cartName = data("name")
    Dim items: items = data("items")
    
    ' Vytvoření nového košíku
    Dim cartId
    sql = "INSERT INTO SavedCarts (UserID, Name, Created, LastModified) " & _
          "VALUES (" & SafeSQL(userId) & ", " & SafeSQL(cartName) & ", GETDATE(), GETDATE()); " & _
          "SELECT SCOPE_IDENTITY() AS CartID"
    
    cartId = GetSingleValue(sql)
    
    ' Uložen<PERSON> košíku
    For Each item In items
        sql = "INSERT INTO SavedCartItems (CartID, ProductID, Quantity) " & _
              "VALUES (" & SafeSQL(cartId) & ", " & SafeSQL(item("productId")) & ", " & SafeSQL(item("quantity")) & ")"
        ExecuteSQL sql
    Next
    
    Response.ContentType = "application/json"
    Response.Write "{""success"": true, ""cartId"": " & cartId & "}"
End If
%>