<?php
// Nastavení pro odesílání e-mailu
$to_email = "<EMAIL>";
$subject = "Nová rychlá poptávka - DINCO FASTENNERS";

// Kontrola, zda byl formulář odeslán metodou POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Sanitizace a validace vstupních dat
    $firstName = htmlspecialchars(trim($_POST['firstName'] ?? ''));
    $lastName = htmlspecialchars(trim($_POST['lastName'] ?? ''));
    $email = filter_var(trim($_POST['email'] ?? ''), FILTER_SANITIZE_EMAIL);
    $phone = htmlspecialchars(trim($_POST['phone'] ?? ''));
    $company = htmlspecialchars(trim($_POST['company'] ?? ''));
    $material = htmlspecialchars(trim($_POST['material'] ?? ''));
    $quantity = htmlspecialchars(trim($_POST['quantity'] ?? ''));
    $specifications = htmlspecialchars(trim($_POST['specifications'] ?? ''));
    $deadline = htmlspecialchars(trim($_POST['deadline'] ?? ''));
    $message = htmlspecialchars(trim($_POST['message'] ?? ''));
    
    // Zpracování vybraných produktů
    $products = [];
    if (isset($_POST['products']) && is_array($_POST['products'])) {
        $product_names = [
            'screws' => 'Metrické šrouby',
            'nuts' => 'Matice',
            'washers' => 'Podložky',
            'bolts' => 'Vruty',
            'threaded-rods' => 'Závitové tyče',
            'other' => 'Ostatní'
        ];
        
        foreach ($_POST['products'] as $product) {
            if (isset($product_names[$product])) {
                $products[] = $product_names[$product];
            }
        }
    }
    
    // Základní validace
    $errors = [];
    
    if (empty($firstName)) {
        $errors[] = "Jméno je povinné";
    }
    
    if (empty($lastName)) {
        $errors[] = "Příjmení je povinné";
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Platný e-mail je povinný";
    }
    
    // Pokud nejsou chyby, odešleme e-mail
    if (empty($errors)) {
        
        // Sestavení obsahu e-mailu
        $email_body = "NOVÁ RYCHLÁ POPTÁVKA\n";
        $email_body .= "========================\n\n";
        
        $email_body .= "KONTAKTNÍ ÚDAJE:\n";
        $email_body .= "Jméno: " . $firstName . " " . $lastName . "\n";
        $email_body .= "E-mail: " . $email . "\n";
        $email_body .= "Telefon: " . ($phone ?: 'Neuvedeno') . "\n";
        $email_body .= "Firma: " . ($company ?: 'Neuvedeno') . "\n\n";
        
        if (!empty($products)) {
            $email_body .= "POŽADOVANÉ PRODUKTY:\n";
            foreach ($products as $product) {
                $email_body .= "- " . $product . "\n";
            }
            $email_body .= "\n";
        }
        
        $email_body .= "SPECIFIKACE:\n";
        $email_body .= "Materiál: " . ($material ?: 'Neuvedeno') . "\n";
        $email_body .= "Množství: " . ($quantity ?: 'Neuvedeno') . "\n";
        $email_body .= "Termín dodání: " . ($deadline ?: 'Neuvedeno') . "\n\n";
        
        if (!empty($specifications)) {
            $email_body .= "DETAILNÍ SPECIFIKACE:\n";
            $email_body .= $specifications . "\n\n";
        }
        
        if (!empty($message)) {
            $email_body .= "DODATEČNÉ INFORMACE:\n";
            $email_body .= $message . "\n\n";
        }
        
        $email_body .= "========================\n";
        $email_body .= "Odesláno: " . date('d.m.Y H:i:s') . "\n";
        $email_body .= "IP adresa: " . $_SERVER['REMOTE_ADDR'] . "\n";
        
        // Hlavičky e-mailu
        $headers = "From: " . $email . "\r\n";
        $headers .= "Reply-To: " . $email . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion();
        
        // Odeslání e-mailu
        if (mail($to_email, $subject, $email_body, $headers)) {
            // Úspěšné odeslání
            $response = [
                'success' => true,
                'message' => 'Děkujeme za vaši poptávku! Ozveme se vám co nejdříve.'
            ];
        } else {
            // Chyba při odesílání
            $response = [
                'success' => false,
                'message' => 'Omlouváme se, došlo k chybě při odesílání. Zkuste to prosím znovu nebo nás kontaktujte přímo.'
            ];
        }
        
    } else {
        // Validační chyby
        $response = [
            'success' => false,
            'message' => 'Chyby ve formuláři: ' . implode(', ', $errors)
        ];
    }
    
    // Pokud je požadavek AJAX, vrátíme JSON
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Jinak přesměrujeme zpět na formulář s parametrem
    $redirect_url = 'rychla-poptavka.html';
    if ($response['success']) {
        $redirect_url .= '?status=success';
    } else {
        $redirect_url .= '?status=error&message=' . urlencode($response['message']);
    }
    
    header('Location: ' . $redirect_url);
    exit;
    
} else {
    // Pokud není POST požadavek, přesměrujeme na formulář
    header('Location: rychla-poptavka.html');
    exit;
}
?>
