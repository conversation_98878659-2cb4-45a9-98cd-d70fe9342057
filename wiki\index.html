<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technické informace - DINCO FASTENNERS</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
        .wiki-hero {
            background-color: #222222;
            padding: 80px 0;
            text-align: center;
        }

        .wiki-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #FFFFFF;
            text-shadow: 1px 3px 0 #969696a4, 1px 13px 5px #aba8a8c2;
        }

        .wiki-hero p {
            font-size: 1.2rem;
            color: #DDDDDD;
            max-width: 600px;
            margin: 0 auto;
        }

        .wiki-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 60px 0;
        }

        .wiki-card {
            background-color: #2A2A2A;
            border: 2px solid #292d31;
            border-radius: 8px;
            padding: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .wiki-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
            border-color: #d0e3f7;
            filter: brightness(1.1);
            text-decoration: none;
        }

        .wiki-card h3 {
            color: #FFFFFF;
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-family: 'Oswald', sans-serif;
        }

        .wiki-card p {
            color: #DDDDDD;
            line-height: 1.6;
            margin-bottom: 0;
        }

        .wiki-icon {
            font-size: 3rem;
            color: #007BFF;
            margin-bottom: 20px;
            display: block;
        }

        .breadcrumb {
            background-color: #333333;
            padding: 15px 0;
        }

        .breadcrumb a {
            color: #00A1FF;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #DDDDDD;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="../index.html"><img src="../images/placeholder-logo.png" alt="DINCO Logo" style="height: 100px;"></a>
            </div>

            <!-- Hamburger menu button pro mobil -->
            <button class="menu-toggle" id="menuToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="../index.html" onclick="closeMenu()">Domů</a></li>
                    <li><a href="../eshop/default.asp" onclick="closeMenu()">E-shop</a></li>
                    <li><a href="#" onclick="closeMenu()">Produkty</a></li>
                    <li><a href="index.html" onclick="closeMenu()">Technické informace</a></li>
                    <li><a href="#" onclick="closeMenu()">O nás</a></li>
                    <li><a href="#" onclick="closeMenu()">Reference</a></li>
                    <li><a href="#" onclick="closeMenu()">Kontakty</a></li>
                </ul>
            </nav>
            <button class="cta-button header-cta" onclick="window.location.href='../rychla-poptavka.html'">Rychlá Poptávka</button>
        </div>

        <!-- Overlay pro mobilní menu -->
        <div class="menu-overlay" id="menuOverlay" onclick="closeMenu()"></div>
    </header>

    <div class="breadcrumb">
        <div class="container">
            <a href="../index.html">Domů</a>
            <span>></span>
            <span>Technické informace</span>
        </div>
    </div>

    <section class="wiki-hero">
        <div class="container">
            <h1>Technické informace</h1>
            <p>Kompletní technická dokumentace pro nerezový spojovací materiál. Vše co potřebujete vědět pro správný výběr a použití.</p>
        </div>
    </section>

    <section>
        <div class="container">
            <div class="wiki-grid">
                <a href="typy.html" class="wiki-card">
                    <span class="wiki-icon">🔩</span>
                    <h3>Typy spojovacích prvků</h3>
                    <p>Přehled všech typů šroubů, matic, podložek a dalších spojovacích prvků s jejich specifickými vlastnostmi a použitím.</p>
                </a>

                <a href="materialy.html" class="wiki-card">
                    <span class="wiki-icon">⚗️</span>
                    <h3>Materiály a vlastnosti</h3>
                    <p>Detailní informace o nerezových ocelích A2, A4 a dalších materiálech včetně jejich chemického složení a vlastností.</p>
                </a>

                <a href="normy.html" class="wiki-card">
                    <span class="wiki-icon">📋</span>
                    <h3>DIN/ISO normy</h3>
                    <p>Kompletní přehled platných norem DIN a ISO pro spojovací materiál s vysvětlením jejich významu a použití.</p>
                </a>

                <a href="tabulky.html" class="wiki-card">
                    <span class="wiki-icon">📊</span>
                    <h3>Technické tabulky</h3>
                    <p>Tabulky pevností, rozměrů, závitů, momentů utažení a dalších technických parametrů pro správnou aplikaci.</p>
                </a>

                <a href="upravy.html" class="wiki-card">
                    <span class="wiki-icon">🛡️</span>
                    <h3>Povrchové úpravy</h3>
                    <p>Přehled povrchových úprav, jejich vlastností a vhodnosti pro různá prostředí a aplikace.</p>
                </a>

                <a href="prostredi.html" class="wiki-card">
                    <span class="wiki-icon">🌍</span>
                    <h3>Použití dle prostředí</h3>
                    <p>Doporučení pro výběr vhodného materiálu podle prostředí - interiér, exteriér, agresivní prostředí, potravinářství.</p>
                </a>

                <a href="porovnani.html" class="wiki-card">
                    <span class="wiki-icon">⚖️</span>
                    <h3>Porovnání materiálů</h3>
                    <p>Srovnání vlastností různých materiálů a jejich vhodnosti pro konkrétní aplikace s praktickými doporučeními.</p>
                </a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container footer-grid">
            <div class="footer-col">
                <h4>DINCO FASTENNERS</h4>
                <p>Váš spolehlivý dodavatel kvalitního nerezového spojovacího materiálu.</p>
            </div>
            <div class="footer-col">
                <h4>Rychlé odkazy</h4>
                <ul>
                    <li><a href="../index.html">Domů</a></li>
                    <li><a href="../eshop/default.asp">E-shop</a></li>
                    <li><a href="index.html">Technické info</a></li>
                    <li><a href="../rychla-poptavka.html">Rychlá poptávka</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4>Kontakt</h4>
                <p>Ulice 123<br>Město, PSČ<br> Telefon: +420 123 456 789<br> Email: <EMAIL></p>
            </div>
            <div class="footer-col">
                <h4>Sledujte nás</h4>
                <p>IČO: 1<br>DIČ: CZ12345678</p>
            </div>
        </div>
        <div class="container footer-bottom">
            <p>&copy; 2025 DINCO FASTENNERS. Všechna práva vyhrazena.</p>
        </div>
    </footer>

    <script>
        // Hamburger menu functionality
        const menuToggle = document.getElementById('menuToggle');
        const mainNav = document.getElementById('mainNav');
        const menuOverlay = document.getElementById('menuOverlay');

        function toggleMenu() {
            menuToggle.classList.toggle('active');
            mainNav.classList.toggle('active');
            menuOverlay.classList.toggle('active');

            if (mainNav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMenu() {
            menuToggle.classList.remove('active');
            mainNav.classList.remove('active');
            menuOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        if (menuToggle) {
            menuToggle.addEventListener('click', toggleMenu);
        }

        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMenu();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMenu();
            }
        });
    </script>
</body>
</html>
