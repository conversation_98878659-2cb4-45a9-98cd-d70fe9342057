<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DIN/ISO normy - DINCO FASTENNERS</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
        .wiki-content { padding: 60px 0; }
        .wiki-article { max-width: 1200px; margin: 0 auto; background-color: #2A2A2A; border: 2px solid #292d31; border-radius: 8px; padding: 40px; }
        .wiki-article h1 { color: #FFFFFF; font-size: 2.5rem; margin-bottom: 30px; font-family: '<PERSON>', sans-serif; }
        .wiki-article h2 { color: #FFFFFF; font-size: 1.8rem; margin: 30px 0 20px 0; font-family: 'Oswald', sans-serif; border-bottom: 2px solid #007BFF; padding-bottom: 10px; }
        .wiki-article h3 { color: #00A1FF; font-size: 1.3rem; margin: 25px 0 15px 0; }
        .wiki-article p { color: #DDDDDD; line-height: 1.8; margin-bottom: 20px; }
        .wiki-article ul, .wiki-article ol { color: #DDDDDD; margin: 20px 0; padding-left: 30px; }
        .wiki-article li { margin-bottom: 8px; line-height: 1.6; }
        .norm-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin: 30px 0; }
        .norm-card { background-color: #333333; border: 1px solid #444444; border-radius: 8px; padding: 25px; transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .norm-card:hover { transform: translateY(-3px); box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3); }
        .norm-card h4 { color: #007BFF; font-size: 1.2rem; margin-bottom: 15px; font-family: 'Oswald', sans-serif; }
        .norm-card p { color: #DDDDDD; font-size: 0.95rem; margin-bottom: 10px; }
        .norm-card .norm-number { color: #00A1FF; font-weight: bold; font-size: 1.1rem; }
        .breadcrumb { background-color: #333333; padding: 15px 0; }
        .breadcrumb a { color: #00A1FF; text-decoration: none; }
        .breadcrumb a:hover { text-decoration: underline; }
        .breadcrumb span { color: #DDDDDD; margin: 0 10px; }
        .back-link { background-color: #007BFF; color: #FFFFFF; padding: 12px 25px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 30px; font-weight: 600; transition: background-color 0.3s ease; }
        .back-link:hover { background-color: #0056b3; text-decoration: none; }
        .norm-table { width: 100%; background-color: #333333; border-radius: 8px; overflow: hidden; margin: 20px 0; }
        .norm-table th, .norm-table td { padding: 12px; text-align: left; border-bottom: 1px solid #444; color: #DDDDDD; }
        .norm-table th { background-color: #444444; color: #FFFFFF; font-weight: bold; }
        .norm-table .norm-col { color: #00A1FF; font-weight: bold; }
        .info-box { background-color: #1a472a; border: 1px solid #28a745; border-radius: 5px; padding: 15px; margin: 20px 0; }
        .info-box h4 { color: #28a745; margin-bottom: 10px; }
        .info-box p { color: #DDDDDD; margin-bottom: 0; }
    </style>
</head>
<body>
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="../index.html"><img src="../images/placeholder-logo.png" alt="DINCO Logo" style="height: 100px;"></a>
            </div>
            <button class="menu-toggle" id="menuToggle"><span></span><span></span><span></span></button>
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="../index.html" onclick="closeMenu()">Domů</a></li>
                    <li><a href="../eshop/default.asp" onclick="closeMenu()">E-shop</a></li>
                    <li><a href="#" onclick="closeMenu()">Produkty</a></li>
                    <li><a href="index.html" onclick="closeMenu()">Technické informace</a></li>
                    <li><a href="#" onclick="closeMenu()">O nás</a></li>
                    <li><a href="#" onclick="closeMenu()">Reference</a></li>
                    <li><a href="#" onclick="closeMenu()">Kontakty</a></li>
                </ul>
            </nav>
            <button class="cta-button header-cta" onclick="window.location.href='../rychla-poptavka.html'">Rychlá Poptávka</button>
        </div>
        <div class="menu-overlay" id="menuOverlay" onclick="closeMenu()"></div>
    </header>

    <div class="breadcrumb">
        <div class="container">
            <a href="../index.html">Domů</a><span>></span><a href="index.html">Technické informace</a><span>></span><span>DIN/ISO normy</span>
        </div>
    </div>

    <section class="wiki-content">
        <div class="container">
            <div class="wiki-article">
                <h1>📋 Přehled DIN/ISO norem</h1>

                <p>Normy DIN (Deutsches Institut für Normung) a ISO (International Organization for Standardization) definují standardy pro spojovací materiál. Tyto normy zajišťují kompatibilitu, kvalitu a bezpečnost produktů po celém světě.</p>

                <div class="info-box">
                    <h4>💡 Důležité informace</h4>
                    <p>Mnoho DIN norem bylo převedeno na ISO normy. V našem sortimentu používáme označení podle nejnovějších platných standardů.</p>
                </div>

                <h2>Šrouby s vnitřním šestihranem</h2>

                <div class="norm-grid">
                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 912 / ISO 4762</span></h4>
                        <p><strong>Šrouby s válcovou hlavou</strong></p>
                        <p>Nejčastěji používané šrouby s vnitřním šestihranem. Válcová hlava umožňuje zapuštění do materiálu.</p>
                        <p><strong>Použití:</strong> Strojírenství, konstrukce, přesná mechanika</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 7991 / ISO 10642</span></h4>
                        <p><strong>Šrouby s kuželovou zapuštěnou hlavou</strong></p>
                        <p>Zapuštěné šrouby pro hladký povrch. Hlava je úplně zapuštěna do materiálu.</p>
                        <p><strong>Použití:</strong> Estetické aplikace, přesné konstrukce</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 7380 / ISO 7380</span></h4>
                        <p><strong>Šrouby s půlkulatou hlavou</strong></p>
                        <p>Nízká půlkulatá hlava kombinuje estetiku s funkčností. Menší výška hlavy.</p>
                        <p><strong>Použití:</strong> Dekorativní aplikace, tenké materiály</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 6912 / ISO 7984</span></h4>
                        <p><strong>Šrouby s nízkou válcovou hlavou</strong></p>
                        <p>Nižší hlava než u DIN 912, vhodné pro omezené prostory.</p>
                        <p><strong>Použití:</strong> Aplikace s omezenou výškou</p>
                    </div>
                </div>

                <h2>Šrouby se šestihrannou hlavou</h2>

                <div class="norm-grid">
                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 933 / ISO 4017</span></h4>
                        <p><strong>Šrouby se šestihrannou hlavou</strong></p>
                        <p>Klasické šrouby s vnějším šestihranem. Vysoká pevnost a snadná montáž.</p>
                        <p><strong>Použití:</strong> Konstrukce, strojírenství, všeobecné aplikace</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 931 / ISO 4014</span></h4>
                        <p><strong>Šrouby se šestihrannou hlavou, částečný závit</strong></p>
                        <p>Závit pouze na části dříku, hladká část pod hlavou.</p>
                        <p><strong>Použití:</strong> Spojení s přesným polohováním</p>
                    </div>
                </div>

                <h2>Matice</h2>

                <table class="norm-table">
                    <thead>
                        <tr>
                            <th>Norma</th>
                            <th>Název</th>
                            <th>Popis</th>
                            <th>Typické použití</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="norm-col">DIN 934 / ISO 4032</td>
                            <td>Šestihranné matice</td>
                            <td>Standardní matice pro všeobecné použití</td>
                            <td>Univerzální aplikace</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 985 / ISO 7040</td>
                            <td>Pojistné matice s nylonovou vložkou</td>
                            <td>Zabraňují samovolnému povolení</td>
                            <td>Vibrační prostředí</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 6923 / ISO 4161</td>
                            <td>Matice s límcem</td>
                            <td>Integrovaná podložka</td>
                            <td>Ochrana povrchu</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 935 / ISO 7036</td>
                            <td>Korunové matice</td>
                            <td>S drážkami pro závlačku</td>
                            <td>Vysoká bezpečnost</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 439 / ISO 4035</td>
                            <td>Nízké matice</td>
                            <td>Snížená výška</td>
                            <td>Omezené prostory</td>
                        </tr>
                    </tbody>
                </table>

                <h2>Podložky</h2>

                <div class="norm-grid">
                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 125 / ISO 7089</span></h4>
                        <p><strong>Ploché podložky</strong></p>
                        <p>Základní typ podložek pro rozložení tlaku a ochranu povrchu.</p>
                        <p><strong>Řada A:</strong> Střední průměr, <strong>Řada B:</strong> Velký průměr</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 127 / ISO 7980</span></h4>
                        <p><strong>Pružné podložky (spirálové)</strong></p>
                        <p>Zabraňují samovolnému povolení šroubového spoje.</p>
                        <p><strong>Typ A:</strong> Pravotočivé, <strong>Typ B:</strong> Levotočivé</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 6798</span></h4>
                        <p><strong>Zubové podložky</strong></p>
                        <p>Vnější nebo vnitřní zuby pro lepší uchycení.</p>
                        <p><strong>Typ A:</strong> Vnější zuby, <strong>Typ J:</strong> Vnitřní zuby</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 6796</span></h4>
                        <p><strong>Talířové pružiny</strong></p>
                        <p>Vysoká pružná síla, konstantní předpětí.</p>
                        <p><strong>Použití:</strong> Dynamické zatížení, kompenzace roztažení</p>
                    </div>
                </div>

                <h2>Vruty</h2>

                <table class="norm-table">
                    <thead>
                        <tr>
                            <th>Norma</th>
                            <th>Název</th>
                            <th>Hlava</th>
                            <th>Použití</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="norm-col">DIN 7981</td>
                            <td>Samořezné vruty</td>
                            <td>Křížová drážka, půlkulatá</td>
                            <td>Tenké plechy</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 7982</td>
                            <td>Samořezné vruty</td>
                            <td>Křížová drážka, zapuštěná</td>
                            <td>Hladký povrch</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 7983</td>
                            <td>Samořezné vruty</td>
                            <td>Křížová drážka, válcová</td>
                            <td>Univerzální použití</td>
                        </tr>
                        <tr>
                            <td class="norm-col">DIN 571</td>
                            <td>Vruty do dřeva</td>
                            <td>Šestihranná</td>
                            <td>Dřevěné konstrukce</td>
                        </tr>
                    </tbody>
                </table>

                <h2>Závitové tyče a příslušenství</h2>

                <div class="norm-grid">
                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 975 / ISO 4762</span></h4>
                        <p><strong>Závitové tyče</strong></p>
                        <p>Standardní metrický závit po celé délce tyče.</p>
                        <p><strong>Délky:</strong> 1m, 2m, 3m (standardní)</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 976</span></h4>
                        <p><strong>Závitové tyče s jemným závitem</strong></p>
                        <p>Jemný závit pro speciální aplikace.</p>
                        <p><strong>Použití:</strong> Přesná mechanika, nastavovací prvky</p>
                    </div>

                    <div class="norm-card">
                        <h4><span class="norm-number">DIN 6334</span></h4>
                        <p><strong>Spojovací matice</strong></p>
                        <p>Dlouhé matice pro spojování závitových tyčí.</p>
                        <p><strong>Délka:</strong> 3× průměr závitu</p>
                    </div>
                </div>

                <h2>Materiálové označení podle norem</h2>

                <table class="norm-table">
                    <thead>
                        <tr>
                            <th>Označení</th>
                            <th>Materiál číslo</th>
                            <th>AISI</th>
                            <th>Popis</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="norm-col">A2</td>
                            <td>1.4301 / 1.4307</td>
                            <td>304 / 304L</td>
                            <td>Austenitická ocel, standardní</td>
                        </tr>
                        <tr>
                            <td class="norm-col">A4</td>
                            <td>1.4401 / 1.4404</td>
                            <td>316 / 316L</td>
                            <td>Austenitická ocel s molybdenem</td>
                        </tr>
                        <tr>
                            <td class="norm-col">A5</td>
                            <td>1.4529</td>
                            <td>-</td>
                            <td>Super austenitická ocel</td>
                        </tr>
                    </tbody>
                </table>

                <div class="info-box">
                    <h4>📞 Potřebujete poradit?</h4>
                    <p>Náš technický tým vám pomůže s výběrem správné normy a materiálu pro vaši aplikaci. Kontaktujte ná<NAME_EMAIL></p>
                </div>

                <a href="index.html" class="back-link">← Zpět na přehled</a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container footer-grid">
            <div class="footer-col">
                <h4>DINCO FASTENNERS</h4>
                <p>Váš spolehlivý dodavatel kvalitního nerezového spojovacího materiálu.</p>
            </div>
            <div class="footer-col">
                <h4>Rychlé odkazy</h4>
                <ul>
                    <li><a href="../index.html">Domů</a></li>
                    <li><a href="../eshop/default.asp">E-shop</a></li>
                    <li><a href="index.html">Technické info</a></li>
                    <li><a href="../rychla-poptavka.html">Rychlá poptávka</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4>Kontakt</h4>
                <p>Ulice 123<br>Město, PSČ<br> Telefon: +420 123 456 789<br> Email: <EMAIL></p>
            </div>
            <div class="footer-col">
                <h4>Sledujte nás</h4>
                <p>IČO: 1<br>DIČ: CZ12345678</p>
            </div>
        </div>
        <div class="container footer-bottom">
            <p>&copy; 2025 DINCO FASTENNERS. Všechna práva vyhrazena.</p>
        </div>
    </footer>

    <script>
        const menuToggle = document.getElementById('menuToggle');
        const mainNav = document.getElementById('mainNav');
        const menuOverlay = document.getElementById('menuOverlay');
        function toggleMenu() { menuToggle.classList.toggle('active'); mainNav.classList.toggle('active'); menuOverlay.classList.toggle('active'); if (mainNav.classList.contains('active')) { document.body.style.overflow = 'hidden'; } else { document.body.style.overflow = ''; } }
        function closeMenu() { menuToggle.classList.remove('active'); mainNav.classList.remove('active'); menuOverlay.classList.remove('active'); document.body.style.overflow = ''; }
        if (menuToggle) { menuToggle.addEventListener('click', toggleMenu); }
        window.addEventListener('resize', function() { if (window.innerWidth > 768) { closeMenu(); } });
        document.addEventListener('keydown', function(e) { if (e.key === 'Escape') { closeMenu(); } });
    </script>
</body>
</html>