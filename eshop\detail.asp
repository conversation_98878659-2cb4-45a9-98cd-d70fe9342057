<%@ CodePage=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<!--#include file="includes/security.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim productUrl, sql, rs
productUrl = Request.QueryString("url")

If productUrl = "" Then
    Response.Redirect "catalog.asp"
    Response.End
End If

' Získání detailu produktu
sql = "SELECT p.*, c.Name AS CategoryName, c.SEOUrl AS CategoryUrl " & _
      "FROM Products p " & _
      "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
      "WHERE p.SEOUrl = " & SafeSQL(productUrl) & " AND p.IsActive = 1"

Set rs = GetRecordset(sql)

If rs.EOF Then
    Response.Redirect "catalog.asp"
    Response.End
End If

Dim pageTitle: pageTitle = rs("Name") & " - " & SITE_NAME
Dim metaDesc: metaDesc = Left(rs("Description"), 155)
%>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <title><%=pageTitle%></title>
    <meta name="description" content="<%=metaDesc%>">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!--#include file="includes/header.asp"-->
    
    <div class="container">
        <nav class="breadcrumb">
            <a href="default.asp">Úvod</a> &gt;
            <a href="catalog.asp">Katalog</a> &gt;
            <a href="catalog.asp?category=<%=Server.URLEncode(rs("CategoryUrl"))%>"><%=rs("CategoryName")%></a> &gt;
            <span><%=rs("Name")%></span>
        </nav>

        <div class="product-detail">
            <div class="product-gallery">
                <% If Not IsNull(rs("MainImage")) And rs("MainImage") <> "" Then %>
                    <img src="<%=PRODUCT_IMAGES_PATH & rs("MainImage")%>" alt="<%=rs("Name")%>" class="main-image">
                <% End If %>
            </div>

            <div class="product-info">
                <h1><%=rs("Name")%></h1>
                <div class="category">Kategorie: <a href="catalog.asp?category=<%=Server.URLEncode(rs("CategoryUrl"))%>"><%=rs("CategoryName")%></a></div>
                
                <div class="price-section">
                    <% If Not IsNull(rs("DiscountPrice")) And rs("DiscountPrice") > 0 Then %>
                        <div class="original-price"><%=FormatCurrency(rs("Price"))%></div>
                        <div class="discount-price"><%=FormatCurrency(rs("DiscountPrice"))%></div>
                        <div class="discount-badge">-<%=Round((1 - rs("DiscountPrice")/rs("Price")) * 100)%>%</div>
                    <% Else %>
                        <div class="price"><%=FormatCurrency(rs("Price"))%></div>
                    <% End If %>
                </div>

                <div class="stock-info">
                    <% If rs("Stock") > 0 Then %>
                        <span class="in-stock">Skladem: <%=rs("Stock")%> ks</span>
                        <div class="add-to-cart">
                            <input type="number" id="quantity" value="1" min="1" max="<%=rs("Stock")%>">
                            <button onclick="cart.addItem(<%=rs("ProductID")%>, document.getElementById('quantity').value)" class="btn btn-primary">Přidat do košíku</button>
                        </div>
                    <% Else %>
                        <span class="out-of-stock">Není skladem</span>
                    <% End If %>
                </div>

                <% If Not IsNull(rs("SKU")) And rs("SKU") <> "" Then %>
                    <div class="sku">Kód produktu: <%=rs("SKU")%></div>
                <% End If %>

                <div class="description">
                    <%=rs("Description")%>
                </div>
            </div>
        </div>
    </div>

    <script src="js/cart.js"></script>
</body>
</html>
<%
rs.Close
Set rs = Nothing
%>