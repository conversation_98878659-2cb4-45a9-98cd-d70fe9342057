<%@ CodePage=65001 %>
<%
' TEST MIGRACE - kontrola připojení a dat

' Konfigurace připojení
Dim oldConnString, newConnString
oldConnString = "Provider=SQLOLEDB;Data Source=GPNEW\SQLEXPRESS;Initial Catalog=shop;Integrated Security=SSPI;"
newConnString = "Provider=SQLOLEDB;Server=c109wq.forpsi.com;Database=f187884;User Id=f187884;Password=**************;"

Sub LogMessage(message)
    Response.Write "<p>" & Now() & ": " & message & "</p>" & vbCrLf
    Response.Flush
End Sub

Response.Write "<html><head><title>Test migrace</title></head><body>"
Response.Write "<h1>Test připojení a dat</h1>"

On Error Resume Next

' Test připojení ke staré databázi
LogMessage "Testování připojení ke staré databázi..."
Dim oldConn
Set oldConn = Server.CreateObject("ADODB.Connection")
oldConn.Open oldConnString

If Err.Number <> 0 Then
    LogMessage "CHYBA: Nelze se připojit ke staré databázi: " & Err.Description
    Response.Write "</body></html>"
    Response.End
Else
    LogMessage "✓ Připojení ke staré databázi úspěšné"
End If

' Test připojení k nové databázi
LogMessage "Testování připojení k nové databázi..."
Dim newConn
Set newConn = Server.CreateObject("ADODB.Connection")
newConn.Open newConnString

If Err.Number <> 0 Then
    LogMessage "CHYBA: Nelze se připojit k nové databázi: " & Err.Description
    Response.Write "</body></html>"
    Response.End
Else
    LogMessage "✓ Připojení k nové databázi úspěšné"
End If

' Kontrola dat ve staré databázi
LogMessage "Kontrola dat ve staré databázi..."

Dim rs
Set rs = Server.CreateObject("ADODB.Recordset")

' Test kategorií
rs.Open "SELECT COUNT(*) as cnt FROM Categories", oldConn
If Err.Number = 0 Then
    LogMessage "Počet kategorií ve staré databázi: " & rs("cnt")
    rs.Close
Else
    LogMessage "Chyba při čtení kategorií: " & Err.Description
    Err.Clear
End If

' Test produktů
rs.Open "SELECT COUNT(*) as cnt FROM Products", oldConn
If Err.Number = 0 Then
    LogMessage "Počet produktů ve staré databázi: " & rs("cnt")
    rs.Close
Else
    LogMessage "Chyba při čtení produktů: " & Err.Description
    Err.Clear
End If

' Ukázka prvních 5 kategorií
LogMessage "Ukázka prvních 5 kategorií:"
rs.Open "SELECT TOP 5 * FROM Categories", oldConn
If Err.Number = 0 Then
    While Not rs.EOF
        LogMessage "- ID: " & rs("CategoryID") & ", Název: " & rs("Name") & ", SEO: " & rs("SEOUrl")
        rs.MoveNext
    Wend
    rs.Close
Else
    LogMessage "Chyba při čtení ukázky kategorií: " & Err.Description
    Err.Clear
End If

' Ukázka prvních 5 produktů
LogMessage "Ukázka prvních 5 produktů:"
rs.Open "SELECT TOP 5 * FROM Products", oldConn
If Err.Number = 0 Then
    While Not rs.EOF
        LogMessage "- ID: " & rs("ProductID") & ", Název: " & rs("Name") & ", Cena: " & rs("Price")
        rs.MoveNext
    Wend
    rs.Close
Else
    LogMessage "Chyba při čtení ukázky produktů: " & Err.Description
    Err.Clear
End If

' Kontrola struktury nové databáze
LogMessage "Kontrola struktury nové databáze..."

rs.Open "SELECT COUNT(*) as cnt FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Categories'", newConn
If Err.Number = 0 Then
    If rs("cnt") > 0 Then
        LogMessage "✓ Tabulka Categories existuje v nové databázi"
    Else
        LogMessage "⚠ Tabulka Categories neexistuje v nové databázi"
    End If
    rs.Close
Else
    LogMessage "Chyba při kontrole tabulky Categories: " & Err.Description
    Err.Clear
End If

rs.Open "SELECT COUNT(*) as cnt FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Products'", newConn
If Err.Number = 0 Then
    If rs("cnt") > 0 Then
        LogMessage "✓ Tabulka Products existuje v nové databázi"
    Else
        LogMessage "⚠ Tabulka Products neexistuje v nové databázi"
    End If
    rs.Close
Else
    LogMessage "Chyba při kontrole tabulky Products: " & Err.Description
    Err.Clear
End If

' Uzavření připojení
oldConn.Close
Set oldConn = Nothing
newConn.Close
Set newConn = Nothing

LogMessage "Test dokončen!"
LogMessage "Pokud jsou všechna připojení úspěšná, můžete spustit plnou migraci."

Response.Write "</body></html>"
%>
