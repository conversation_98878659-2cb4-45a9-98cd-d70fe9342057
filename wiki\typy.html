<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typy spojovac<PERSON>ch prvků - DINCO FASTENNERS</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
        .wiki-content {
            padding: 60px 0;
        }

        .wiki-article {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #2A2A2A;
            border: 2px solid #292d31;
            border-radius: 8px;
            padding: 40px;
        }

        .wiki-article h1 {
            color: #FFFFFF;
            font-size: 2.5rem;
            margin-bottom: 30px;
            font-family: '<PERSON>', sans-serif;
        }

        .wiki-article h2 {
            color: #FFFFFF;
            font-size: 1.8rem;
            margin: 30px 0 20px 0;
            font-family: 'Oswald', sans-serif;
            border-bottom: 2px solid #007BFF;
            padding-bottom: 10px;
        }

        .wiki-article h3 {
            color: #00A1FF;
            font-size: 1.3rem;
            margin: 25px 0 15px 0;
        }

        .wiki-article p {
            color: #DDDDDD;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .wiki-article ul, .wiki-article ol {
            color: #DDDDDD;
            margin: 20px 0;
            padding-left: 30px;
        }

        .wiki-article li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .type-card {
            background-color: #333333;
            border: 1px solid #444444;
            border-radius: 8px;
            padding: 25px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .type-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .type-card h4 {
            color: #007BFF;
            font-size: 1.2rem;
            margin-bottom: 15px;
            font-family: 'Oswald', sans-serif;
        }

        .type-card p {
            color: #DDDDDD;
            font-size: 0.95rem;
            margin-bottom: 0;
        }

        .breadcrumb {
            background-color: #333333;
            padding: 15px 0;
        }

        .breadcrumb a {
            color: #00A1FF;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #DDDDDD;
            margin: 0 10px;
        }

        .back-link {
            background-color: #007BFF;
            color: #FFFFFF;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin-top: 30px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }

        .back-link:hover {
            background-color: #0056b3;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="../index.html"><img src="../images/placeholder-logo.png" alt="DINCO Logo" style="height: 100px;"></a>
            </div>

            <button class="menu-toggle" id="menuToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="../index.html" onclick="closeMenu()">Domů</a></li>
                    <li><a href="../eshop/default.asp" onclick="closeMenu()">E-shop</a></li>
                    <li><a href="#" onclick="closeMenu()">Produkty</a></li>
                    <li><a href="index.html" onclick="closeMenu()">Technické informace</a></li>
                    <li><a href="#" onclick="closeMenu()">O nás</a></li>
                    <li><a href="#" onclick="closeMenu()">Reference</a></li>
                    <li><a href="#" onclick="closeMenu()">Kontakty</a></li>
                </ul>
            </nav>
            <button class="cta-button header-cta" onclick="window.location.href='../rychla-poptavka.html'">Rychlá Poptávka</button>
        </div>

        <div class="menu-overlay" id="menuOverlay" onclick="closeMenu()"></div>
    </header>

    <div class="breadcrumb">
        <div class="container">
            <a href="../index.html">Domů</a>
            <span>></span>
            <a href="index.html">Technické informace</a>
            <span>></span>
            <span>Typy spojovacích prvků</span>
        </div>
    </div>

    <section class="wiki-content">
        <div class="container">
            <div class="wiki-article">
                <h1>🔩 Typy spojovacích prvků</h1>

                <p>Spojovací prvky jsou základním stavebním kamenem mnoha konstrukcí a zařízení. Správný výběr typu spojovacího prvku je klíčový pro zajištění spolehlivosti, bezpečnosti a dlouhodobé funkčnosti celé konstrukce.</p>

                <h2>Šrouby</h2>

                <div class="type-grid">
                    <div class="type-card">
                        <h4>Šrouby s válcovou hlavou (DIN 912)</h4>
                        <p>Šrouby s vnitřním šestihranem, ideální pro aplikace kde je potřeba zapuštěná hlava. Vysoká pevnost a přesnost.</p>
                    </div>

                    <div class="type-card">
                        <h4>Šrouby s kuželovou hlavou (DIN 7991)</h4>
                        <p>Zapuštěné šrouby s vnitřním šestihranem pro hladký povrch. Vhodné pro estetické aplikace.</p>
                    </div>

                    <div class="type-card">
                        <h4>Šrouby se šestihrannou hlavou (DIN 933)</h4>
                        <p>Klasické šrouby s vnějším šestihranem. Vysoká pevnost, snadná montáž standardním nářadím.</p>
                    </div>

                    <div class="type-card">
                        <h4>Šrouby s půlkulatou hlavou (DIN 7380)</h4>
                        <p>Nízká půlkulatá hlava s vnitřním šestihranem. Kombinace estetiky a funkčnosti.</p>
                    </div>
                </div>

                <h2>Matice</h2>

                <div class="type-grid">
                    <div class="type-card">
                        <h4>Šestihranné matice (DIN 934)</h4>
                        <p>Standardní matice pro všeobecné použití. Dostupné v různých pevnostních třídách.</p>
                    </div>

                    <div class="type-card">
                        <h4>Pojistné matice (DIN 985)</h4>
                        <p>Matice s nylonovou vložkou zabraňující samovolnému povolení. Ideální pro vibrační prostředí.</p>
                    </div>

                    <div class="type-card">
                        <h4>Matice s límcem (DIN 6923)</h4>
                        <p>Matice s integrovanou podložkou pro lepší rozložení tlaku a ochranu povrchu.</p>
                    </div>

                    <div class="type-card">
                        <h4>Korunové matice (DIN 935)</h4>
                        <p>Matice s drážkami pro zajištění závlačkou. Vysoká bezpečnost proti povolení.</p>
                    </div>
                </div>

                <h2>Podložky</h2>

                <div class="type-grid">
                    <div class="type-card">
                        <h4>Ploché podložky (DIN 125)</h4>
                        <p>Základní typ podložek pro rozložení tlaku a ochranu povrchu materiálu.</p>
                    </div>

                    <div class="type-card">
                        <h4>Pružné podložky (DIN 127)</h4>
                        <p>Spirálové podložky zabraňující samovolnému povolení šroubového spoje.</p>
                    </div>

                    <div class="type-card">
                        <h4>Zubové podložky (DIN 6798)</h4>
                        <p>Podložky se zuby pro lepší uchycení a zabránění rotaci.</p>
                    </div>

                    <div class="type-card">
                        <h4>Těsnící podložky</h4>
                        <p>Speciální podložky s těsnící funkcí pro aplikace vyžadující utěsnění.</p>
                    </div>
                </div>

                <h2>Vruty</h2>

                <h3>Samořezné vruty</h3>
                <ul>
                    <li><strong>DIN 7981:</strong> Vruty s křížovou drážkou pro tenké plechy</li>
                    <li><strong>DIN 7982:</strong> Vruty se zapuštěnou hlavou</li>
                    <li><strong>DIN 7983:</strong> Vruty s půlkulatou hlavou</li>
                </ul>

                <h3>Vruty do dřeva</h3>
                <ul>
                    <li><strong>DIN 571:</strong> Šestihranné vruty do dřeva</li>
                    <li><strong>Univerzální vruty:</strong> Pro různé typy materiálů</li>
                </ul>

                <h2>Závitové tyče a příslušenství</h2>

                <p>Závitové tyče umožňují vytvoření spojů na míru podle konkrétních požadavků. Dostupné v různých délkách a průměrech s metrických závitem.</p>

                <ul>
                    <li><strong>Závitové tyče DIN 975:</strong> Standardní metrický závit</li>
                    <li><strong>Závitové tyče DIN 976:</strong> S jemným závitem</li>
                    <li><strong>Spojovací matice:</strong> Pro spojování závitových tyčí</li>
                    <li><strong>Koncové matice:</strong> Pro ukončení závitových tyčí</li>
                </ul>

                <h2>Speciální spojovací prvky</h2>

                <div class="type-grid">
                    <div class="type-card">
                        <h4>Nýty</h4>
                        <p>Trvalé spojení materiálů, vysoká pevnost ve smyku. Ideální pro konstrukce vystavené vibracím.</p>
                    </div>

                    <div class="type-card">
                        <h4>Závlačky</h4>
                        <p>Zajišťovací prvky pro korunové matice a čepy. Jednoduchá montáž a demontáž.</p>
                    </div>

                    <div class="type-card">
                        <h4>Kolíky</h4>
                        <p>Přesné polohování a spojování součástí. Různé typy podle aplikace.</p>
                    </div>

                    <div class="type-card">
                        <h4>Kotevní prvky</h4>
                        <p>Speciální prvky pro kotvení do betonu, zdiva a dalších materiálů.</p>
                    </div>
                </div>

                <a href="index.html" class="back-link">← Zpět na přehled</a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container footer-grid">
            <div class="footer-col">
                <h4>DINCO FASTENNERS</h4>
                <p>Váš spolehlivý dodavatel kvalitního nerezového spojovacího materiálu.</p>
            </div>
            <div class="footer-col">
                <h4>Rychlé odkazy</h4>
                <ul>
                    <li><a href="../index.html">Domů</a></li>
                    <li><a href="../eshop/default.asp">E-shop</a></li>
                    <li><a href="index.html">Technické info</a></li>
                    <li><a href="../rychla-poptavka.html">Rychlá poptávka</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4>Kontakt</h4>
                <p>Ulice 123<br>Město, PSČ<br> Telefon: +420 123 456 789<br> Email: <EMAIL></p>
            </div>
            <div class="footer-col">
                <h4>Sledujte nás</h4>
                <p>IČO: 1<br>DIČ: CZ12345678</p>
            </div>
        </div>
        <div class="container footer-bottom">
            <p>&copy; 2025 DINCO FASTENNERS. Všechna práva vyhrazena.</p>
        </div>
    </footer>

    <script>
        // Hamburger menu functionality
        const menuToggle = document.getElementById('menuToggle');
        const mainNav = document.getElementById('mainNav');
        const menuOverlay = document.getElementById('menuOverlay');

        function toggleMenu() {
            menuToggle.classList.toggle('active');
            mainNav.classList.toggle('active');
            menuOverlay.classList.toggle('active');

            if (mainNav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMenu() {
            menuToggle.classList.remove('active');
            mainNav.classList.remove('active');
            menuOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        if (menuToggle) {
            menuToggle.addEventListener('click', toggleMenu);
        }

        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMenu();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMenu();
            }
        });
    </script>
</body>
</html>