
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    background-color: #1A1A1A;
    /* Tmavé pozadí */
    color: #DDDDDD;
    /* <PERSON><PERSON><PERSON><PERSON>ý text */
    line-height: 1.6;
}

h1,
h2,
h3,
h4 {
    font-family: '<PERSON>', sans-serif;
    /* Písmo pro nadpisy */
    color: #FFFFFF;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    line-height: 1.2;
    text-shadow: 0px 0px 5px #000000;
}

h2 {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

h3 {
    font-size: 1.5rem;
}


/* Podtržení pod H2 */

h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: #007BFF;
    /* Ak<PERSON><PERSON> modrá */
    /* background-color: #FF7F00; */
    /* Alternativní <PERSON> */
    margin: 0.5rem auto 0;
}

a {
    color: #00A1FF;
    /* <PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON> modr<PERSON> pro odkazy */
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    list-style: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}


/* Hlavička */

.header {
    background-color: #1A1A1A;
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid #333;
    /* Logo jako pozadí hlavičky */
    background-image: url('../images/logo.png');
    background-repeat: no-repeat;
    background-position: center left 20px;
    background-size: auto 40px;
    opacity: 0.95;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Logo overlay pro mobilní */
.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../images/logo.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.1;
    z-index: -1;
    pointer-events: none;
}

/* Logo styling */
.logo img {
    height: 60px;
    transition: height 0.3s ease;
}

/* Hamburger menu button */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #FFFFFF;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    position: relative;
}

.main-nav ul {
    display: flex;
}

.main-nav ul li {
    margin-left: 25px;
}

.main-nav ul li a {
    color: #FFFFFF;
    font-size: 1rem;
    padding-bottom: 5px;
    transition: color 0.3s ease, border-bottom-color 0.3s ease;
    border-bottom: 2px solid transparent;
}

.main-nav ul li a:hover {
    color: #00A1FF;
    border-bottom-color: #00A1FF;
    text-decoration: none;
}


/* CTA Tlačítka */

.cta-button {
    padding: 10px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
}

.header-cta {
    background-color: #007BFF;
    /* Modrá */
    /* background-color: #FF7F00; */
    /* Oranžová */
    color: #FFFFFF;
    margin-left: 20px;
    /* Odsazení od navigace */
    -webkit-box-shadow: 5px 5px 15px 5px #000000; 
    box-shadow: 5px 5px 15px 5px #000000;

}

.header-cta:hover {
    background-color: #0056b3;
    /* Tmavší modrá */
    /* background-color: #cc6600; */
    /* Tmavší oranžová */
    -webkit-box-shadow: 0px 0px 9px 3px #FFFBC4; 
    box-shadow: 0px 0px 9px 3px #FFFBC4;
}

 
/* Hero Sekce */

.hero {
    height: 75vh;
    /* Výška sekce */
    min-height: 400px;
    position: relative;
    display: flex;
    align-items: center;
    
    background-position: top 0px right 0px;
    background-repeat: no-repeat;
    background-image: url('../images/bckr2.png');
    background-size: cover;
    /* Zástupný obrázek */
    /* Vložte sem cestu k vašemu obrázku pozadí */
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    /* Tmavý overlay */
    z-index: 1;
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
    max-width: 62%;
    /* Obsah zabírá 60% šířky */
    margin-bottom: 10px;
    /* Odsazení od levého okraje */
}

.hero-content h1 {
    margin-bottom: 1.5rem;
}

.hero-content p {
    font-size: 1.1rem;
    margin-bottom: 0rem;
    color: #DDDDDD;
}

.hero-buttons .cta-button {
    margin-right: 15px;
}

.hero-buttons .primary {
    background-color: #007BFF;
    /* Modrá */
    /* background-color: #FF7F00; */
    /* Oranžová */
    color: #FFFFFF;
}

.hero-buttons .primary:hover {
    background-color: #0056b3;
    /* Tmavší modrá */
    /* background-color: #cc6600; */
    /* Tmavší oranžová */
    -webkit-box-shadow: 0px 0px 9px 3px #FFFBC4; 
    box-shadow: 0px 0px 9px 3px #FFFBC4;
}

.hero-buttons .secondary {
    background-color: transparent;
    color: #FFFFFF;
    border: 2px solid #FFFFFF;
}

.hero-buttons .secondary:hover {
    background-color: #FFFFFF;
    color: #1A1A1A;
}


/* Sekce Kategorie */

.categories {
    padding: 60px 0;
    background-color: #222222;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    /* Responzivní mřížka */
    gap: 30px;
}

.category-item {
    background-color: #2A2A2A;
    padding: 30px;
    border-radius: 8px;
    border-width: 2px;
    border-style: solid;
    border-color: #292d31;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('../images/s.png');
    background-size: cover;
    /* Pro category-link */
}
.category-item1 {
    background-color: #2A2A2A;
    padding: 30px;
    border-radius: 8px;
    border-width: 2px;
    border-style: solid;
    border-color: #292d31;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('../images/m.png');
    background-size: cover;
    /* Pro category-link */
}
.category-item2 {
    background-color: #2A2A2A;
    padding: 30px;
    border-radius: 8px;
    border-width: 2px;
    border-style: solid;
    border-color: #292d31;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('../images/p.png');
    background-size: cover;
    /* Pro category-link */
}
.category-item3 {
    background-color: #2A2A2A;
    padding: 30px;
    border-radius: 8px;
    border-width: 2px;
    border-style: solid;
    border-color: #292d31;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('../images/v.png');
    background-size: cover;
    /* Pro category-link */
}
.category-item:hover,
.category-item1:hover,
.category-item2:hover,
.category-item3:hover
{
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
    border-width: 2px;
    border-style: solid;
    border-color: #d0e3f7;
    filter: brightness(1.6) contrast(1.2);
}

.category-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #00A1FF;

    /* Barva ikon */
    /* Zde vložte skutečné ikony (např. SVG nebo Font Awesome) */
}

.category-item,
.category-item1,
.category-item2,
.category-item3
 h3 {
    margin-bottom: 0.5rem;
}

.category-item,
.category-item1,
.category-item2,
.category-item3
.category-item 

 p {
    font-size: 0.9rem;
    color: #BBBBBB;
}


/* Proklik na celou kategorii */

.category-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    /* Nad ostatním obsahem uvnitř itemu */
}


/* Sekce Výhody */

.benefits {
    padding: 60px 0;
    background-color: #1A1A1A;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.benefit-item {
    text-align: center;
    background-image: url('/images/tech.png');
    background-size: 50%;
    background-position: top 10px left 10px;
    background-repeat: no-repeat;
    border-width: 2px;
    border-style: solid;
    border-color: #d0e3f7;
    height: 150px;
}
.benefit-item1 {
    text-align: center;
    background-image: url('/images/cert.png');
    background-size: 60%;
    background-position: top 10px left 10px;
    background-repeat: no-repeat;
}
.benefit-item2 {
    text-align: center;
    background-size: 40%;
    background-image: url('/images/sklad.png');
    background-position: top 10px left 10px;
    background-repeat: no-repeat;
}
.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #00A1FF;
    /* Barva ikon */
    /* Zde vložte skutečné ikony */
}

.benefit-item h3 {
    margin-bottom: 0.5rem;
}

.benefit-item p {
    color: #BBBBBB;
}


/* Patička */

.footer {
    background-color: #111111;
    padding: 40px 0 20px;
    color: #AAAAAA;
    font-size: 0.9rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-col h4 {
    color: #FFFFFF;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-col ul li {
    margin-bottom: 0.5rem;
}

.footer-col ul li a {
    color: #AAAAAA;
    transition: color 0.3s ease;
}

.footer-col ul li a:hover {
    color: #FFFFFF;
    text-decoration: none;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #333333;
    padding-top: 20px;
    font-size: 0.8rem;
}


/* Základní Responzivita */

@media (max-width: 992px) {
    .hero-content {
        max-width: 80%;
    }
    .footer-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    h2 {
        font-size: 1.8rem;
    }

    /* Mobilní hlavička */
    .header {
        padding: 10px 0;
        background-position: center;
        background-size: auto 30px;
    }

    /* Logo na mobilu */
    .logo img {
        height: 40px;
    }

    .header-container {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    /* Zobrazit hamburger menu */
    .menu-toggle {
        display: block;
        order: 2;
    }

    /* Skrýt/zobrazit hlavní navigaci */
    .main-nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        background-color: rgba(26, 26, 26, 0.98);
        backdrop-filter: blur(10px);
        transition: left 0.3s ease;
        z-index: 1000;
        padding-top: 80px;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
    }

    .main-nav.active {
        left: 0;
    }

    .main-nav ul {
        flex-direction: column;
        align-items: flex-start;
        padding: 0 30px;
    }

    .main-nav ul li {
        margin-left: 0;
        margin-bottom: 20px;
        width: 100%;
        border-bottom: 1px solid #333;
        padding-bottom: 15px;
    }

    .main-nav ul li a {
        font-size: 1.2rem;
        padding: 10px 0;
        display: block;
        width: 100%;
    }

    .header-cta {
        margin-top: 0;
        margin-left: 0;
        padding: 8px 15px;
        font-size: 0.9rem;
        order: 1;
    }

    /* Overlay pro otevřené menu */
    .menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Animace hamburger menu */
    .menu-toggle span {
        display: block;
        width: 25px;
        height: 3px;
        background-color: #FFFFFF;
        margin: 5px 0;
        transition: 0.3s;
        transform-origin: center;
    }

    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    .hero {
        height: auto;
        padding: 80px 0;
    }
    .hero-content {
        max-width: 100%;
        text-align: center;
    }
    .hero-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .hero-buttons .cta-button {
        margin-right: 0;
        margin-bottom: 10px;
        width: 80%;
    }
    .category-grid {
        grid-template-columns: 1fr;
    }
    /* Kategorie pod sebou */
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    /* Výhody pod sebou */
    .footer-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }
    /* Patička pod sebou */
    .footer-col {
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .hero {
        height: auto;
        padding: 80px 0;
    }
    .hero-content {
        max-width: 100%;
        text-align: center;
    }
    .hero-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .hero-buttons .cta-button {
        margin-right: 0;
        margin-bottom: 10px;
        width: 80%;
    }
    .category-grid {
        grid-template-columns: 1fr;
    }
    /* Kategorie pod sebou */
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    /* Výhody pod sebou */
    .footer-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }
    /* Patička pod sebou */
    .footer-col {
        margin-bottom: 20px;
    }
}
