<%@ CodePage=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<!--#include file="includes/security.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim message

' Zpracován<PERSON> formuláře
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim email, password, firstName, lastName
    
    email = Trim(Request.Form("email"))
    password = Request.Form("password")
    firstName = Trim(Request.Form("firstName"))
    lastName = Trim(Request.Form("lastName"))
    
    ' Validace
    If email = "" Or password = "" Or firstName = "" Or lastName = "" Then
        message = "Všechna pole jsou povinná."
    ElseIf Not ValidateEmail(email) Then
        message = "Neplatný formát e-mailu."
    ElseIf Len(password) < 6 Then
        message = "<PERSON>slo musí mít alespoň 6 znaků."
    Else
        ' <PERSON><PERSON><PERSON><PERSON>, zda už admin neexistuje
        Dim existingAdmin
        existingAdmin = GetSingleValue("SELECT COUNT(*) FROM Users WHERE IsAdmin = 1")
        
        If existingAdmin > 0 Then
            message = "Admin účet již existuje."
        Else
            ' Vytvoření admin účtu
            Dim hashedPassword, sql
            hashedPassword = HashPassword(password)
            
            sql = "INSERT INTO Users (Email, Password, FirstName, LastName, IsAdmin, IsActive, Created) VALUES (" & _
                  SafeSQL(email) & ", " & SafeSQL(hashedPassword) & ", " & _
                  SafeSQL(firstName) & ", " & SafeSQL(lastName) & ", 1, 1, GETDATE())"
            
            ExecuteSQL sql
            message = "Admin účet byl úspěšně vytvořen. Můžete se nyní přihlásit."
        End If
    End If
End If

' Kontrola, zda už admin existuje
Dim adminExists
adminExists = GetSingleValue("SELECT COUNT(*) FROM Users WHERE IsAdmin = 1")
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vytvoření admin účtu - DINCO</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-setup-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .setup-form {
        background: #2A2A2A;
        padding: 30px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    .setup-form h1 {
        text-align: center;
        margin-bottom: 30px;
        color: #FFFFFF;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #DDDDDD;
        font-weight: 500;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px;
        border: 2px solid #444444;
        border-radius: 5px;
        background-color: #333333;
        color: #FFFFFF;
        font-size: 16px;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #007BFF;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        text-align: center;
    }
    
    .message.success {
        background: #28a745;
        color: white;
        border: 1px solid #1e7e34;
    }
    
    .message.error {
        background: #dc3545;
        color: white;
        border: 1px solid #c82333;
    }
    
    .message.info {
        background: #17a2b8;
        color: white;
        border: 1px solid #138496;
    }
    
    .required {
        color: #FF6B6B;
    }
    
    .admin-exists {
        text-align: center;
        padding: 40px;
        color: #DDDDDD;
    }
    
    .admin-exists h2 {
        color: #FFFFFF;
        margin-bottom: 20px;
    }
    
    .admin-exists a {
        color: #007BFF;
        text-decoration: none;
        font-weight: 600;
    }
    
    .admin-exists a:hover {
        text-decoration: underline;
    }
    </style>
</head>
<body>
    <div class="admin-setup-container">
        <% If adminExists > 0 Then %>
            <div class="admin-exists">
                <h2>Admin účet již existuje</h2>
                <p>V systému již existuje administrátorský účet.</p>
                <p><a href="login.asp">Přihlásit se</a> | <a href="default.asp">Zpět na e-shop</a></p>
            </div>
        <% Else %>
            <div class="setup-form">
                <h1>Vytvoření admin účtu</h1>
                
                <% If message <> "" Then %>
                    <div class="message <% If InStr(message, "úspěšně") > 0 Then %>success<% Else %>error<% End If %>">
                        <%=message%>
                    </div>
                <% End If %>
                
                <% If InStr(message, "úspěšně") = 0 Then %>
                    <div class="message info">
                        Vytvořte první administrátorský účet pro správu e-shopu.
                    </div>
                    
                    <form method="post">
                        <div class="form-group">
                            <label for="firstName">Jméno <span class="required">*</span></label>
                            <input type="text" id="firstName" name="firstName" value="<%=Request.Form("firstName")%>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="lastName">Příjmení <span class="required">*</span></label>
                            <input type="text" id="lastName" name="lastName" value="<%=Request.Form("lastName")%>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">E-mail <span class="required">*</span></label>
                            <input type="email" id="email" name="email" value="<%=Request.Form("email")%>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Heslo <span class="required">*</span></label>
                            <input type="password" id="password" name="password" required>
                            <small style="color: #BBBBBB;">Minimálně 6 znaků</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                            Vytvořit admin účet
                        </button>
                    </form>
                <% Else %>
                    <div style="text-align: center; margin-top: 20px;">
                        <a href="login.asp" class="btn btn-primary">Přihlásit se</a>
                        <a href="default.asp" class="btn btn-secondary" style="margin-left: 10px;">Zpět na e-shop</a>
                    </div>
                <% End If %>
            </div>
        <% End If %>
    </div>
</body>
</html>
