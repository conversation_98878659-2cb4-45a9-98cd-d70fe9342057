# DINCO E-shop - Instalačn<PERSON> pokyny

## Provedené úpravy

### 1. Databázové připojení
- ✅ Změněno v `includes/database.asp`
- ✅ Connection string nastaven na: `Server=c109wq.forpsi.com;Database=f187884;User Id=f187884;Password=*****`

### 2. Design a styling
- ✅ Kompletně přepracován CSS v `css/styles.css`
- ✅ Tmavý DINCO design s konzistentními barvami
- ✅ Responzivní design pro mobily
- ✅ Google Fonts (Montserrat + Oswald)

### 3. Header a navigace
- ✅ Jednotný header s DINCO logem
- ✅ Hamburger menu pro mobily
- ✅ Propojení s hlavním webem

### 4. Opravené cesty pro podadresář
- ✅ `includes/config.asp` - přidána konstanta ESHOP_BASE_PATH
- ✅ `includes/header.asp` - relativní cesty
- ✅ `default.asp` - Google Fonts a footer
- ✅ `catalog.asp` - file includes místo virtual
- ✅ `cart.asp` - Google Fonts
- ✅ `login.asp` - Google Fonts
- ✅ `register.asp` - Google Fonts

### 5. Propojení s hlavním webem
- ✅ Tlačítko "E-shop" v hlavní navigaci
- ✅ "Prohlédnout katalog" vede do e-shopu
- ✅ Zpětný odkaz z e-shopu

## Zbývající úkoly

### 1. Dokončení hesla v databázi
V souboru `includes/database.asp` na řádku 5 nahraďte `*****` skutečným heslem.

### 2. Migrace databáze
Spusťte `migrate-database.asp` pro přenos dat ze staré databáze:
1. Upravte heslo v `migrate-database.asp`
2. Odkomentujte bezpečnostní kontroly
3. Spusťte v prohlížeči

### 3. Oprava zbývajících souborů
Spusťte `fix-paths.asp` pro automatickou opravu cest v ostatních souborech.

### 4. Soubory vyžadující ruční kontrolu
- `product.asp` / `detail.asp`
- `reset-password.asp`
- `logout.asp`
- `account/dashboard.asp`
- `account/history.asp`
- `admin/products.asp`

### 5. Testování
Po dokončení otestujte:
- [ ] Načítání hlavní stránky e-shopu
- [ ] Navigace mezi stránkami
- [ ] Responzivní design na mobilu
- [ ] Propojení s hlavním webem
- [ ] Databázové připojení
- [ ] Registrace/přihlášení

## Struktura souborů

```
eshop/
├── css/
│   └── styles.css (✅ upraven)
├── js/
│   └── cart.js
├── images/
├── includes/
│   ├── config.asp (✅ upraven)
│   ├── database.asp (✅ upraven)
│   ├── header.asp (✅ upraven)
│   ├── functions.asp
│   ├── security.asp
│   └── session.asp
├── account/
│   ├── dashboard.asp (⚠ vyžaduje opravu cest)
│   └── history.asp (⚠ vyžaduje opravu cest)
├── admin/
│   └── products.asp (⚠ vyžaduje opravu cest)
├── api/
├── db/
├── default.asp (✅ upraven)
├── catalog.asp (✅ upraven)
├── cart.asp (✅ upraven)
├── login.asp (✅ upraven)
├── register.asp (✅ upraven)
├── product.asp (⚠ vyžaduje kontrolu)
├── detail.asp (⚠ vyžaduje kontrolu)
├── reset-password.asp (⚠ vyžaduje kontrolu)
├── logout.asp (⚠ vyžaduje kontrolu)
├── migrate-database.asp (🆕 nový)
├── fix-paths.asp (🆕 nový)
└── INSTALACE.md (🆕 tento soubor)
```

## Poznámky

- E-shop je nyní plně integrován do DINCO designu
- Všechny hlavní stránky mají konzistentní vzhled
- Responzivní design funguje na všech zařízeních
- Databázové připojení je připraveno pro Forpsi hosting

## Kontakt
Pro technickou podporu kontaktujte: <EMAIL>
