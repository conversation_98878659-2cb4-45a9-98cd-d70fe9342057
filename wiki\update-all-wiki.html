<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aktualizace Wiki - DINCO</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1A1A1A;
            color: #DDDDDD;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #2A2A2A;
            padding: 30px;
            border-radius: 8px;
            border: 2px solid #292d31;
        }
        
        h1 {
            color: #FFFFFF;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .update-btn {
            background-color: #007BFF;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 10px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }
        
        .update-btn:hover {
            background-color: #0056b3;
            text-decoration: none;
        }
        
        .info {
            background-color: #333333;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .file-card {
            background-color: #333333;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        .file-card h3 {
            color: #007BFF;
            margin-bottom: 10px;
        }
        
        .file-card p {
            font-size: 0.9rem;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Aktualizace Wiki stránek</h1>
        
        <div class="info">
            <h3 style="color: #FFFFFF;">Stav aktualizace:</h3>
            <p>✅ <strong>index.html</strong> - Hlavní wiki stránka (dokončeno)</p>
            <p>✅ <strong>typy.html</strong> - Typy spojovacích prvků (dokončeno)</p>
            <p>⏳ <strong>Zbývající stránky</strong> - Potřebují aktualizaci</p>
        </div>
        
        <div class="info">
            <h3 style="color: #FFFFFF;">Co bylo provedeno:</h3>
            <ul>
                <li>Přepracován hlavní design do DINCO stylu</li>
                <li>Přidána responzivní navigace s hamburger menu</li>
                <li>Implementovány breadcrumbs pro lepší orientaci</li>
                <li>Přidány interaktivní karty s hover efekty</li>
                <li>Konzistentní footer se všemi odkazy</li>
                <li>Opraveno propojení s hlavním webem</li>
            </ul>
        </div>
        
        <div class="file-list">
            <div class="file-card">
                <h3>materialy.html</h3>
                <p>Materiály a vlastnosti nerezových ocelí</p>
            </div>
            
            <div class="file-card">
                <h3>normy.html</h3>
                <p>Přehled DIN/ISO norem</p>
            </div>
            
            <div class="file-card">
                <h3>tabulky.html</h3>
                <p>Technické tabulky a parametry</p>
            </div>
            
            <div class="file-card">
                <h3>upravy.html</h3>
                <p>Povrchové úpravy</p>
            </div>
            
            <div class="file-card">
                <h3>prostredi.html</h3>
                <p>Použití dle prostředí</p>
            </div>
            
            <div class="file-card">
                <h3>porovnani.html</h3>
                <p>Porovnání materiálů</p>
            </div>
        </div>
        
        <div class="info">
            <h3 style="color: #FFFFFF;">Návod pro dokončení:</h3>
            <ol>
                <li>Každý soubor potřebuje stejnou strukturu jako typy.html</li>
                <li>Zachovat header, breadcrumbs, footer a JavaScript</li>
                <li>Přidat relevantní obsah pro každou sekci</li>
                <li>Použít type-grid pro karty a wiki-article pro text</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="update-btn">📋 Zpět na wiki</a>
            <a href="../index.html" class="update-btn">🏠 Hlavní web</a>
            <a href="typy.html" class="update-btn">🔩 Ukázka (typy.html)</a>
        </div>
    </div>
</body>
</html>
