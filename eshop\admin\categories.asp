<%@ CodePage=65001 %>
<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/security.asp"-->
<%
' Kontrola přihlášení administrátora
If Session("UserID") = "" Or Session("IsAdmin") <> "1" Then
    Response.Redirect "../login.asp?returnUrl=" & Server.URLEncode(Request.ServerVariables("SCRIPT_NAME"))
End If

Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim action, message
action = Request.QueryString("action")

' Zpracování formuláře
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim categoryId, name, description, seoUrl
    
    name = Trim(Request.Form("name"))
    description = Trim(Request.Form("description"))
    seoUrl = CreateSEOUrl(name)
    
    If name <> "" Then
        Select Case Request.Form("action")
            Case "add"
                Dim sql
                sql = "INSERT INTO Categories (Name, Description, SEOUrl, IsActive, Created) VALUES (" & _
                      SafeSQL(name) & ", " & SafeSQL(description) & ", " & SafeSQL(seoUrl) & ", 1, GETDATE())"
                ExecuteSQL sql
                message = "Kategorie byla úspěšně přidána."
                
            Case "edit"
                categoryId = Request.Form("categoryId")
                sql = "UPDATE Categories SET Name = " & SafeSQL(name) & ", " & _
                      "Description = " & SafeSQL(description) & ", " & _
                      "SEOUrl = " & SafeSQL(seoUrl) & " " & _
                      "WHERE CategoryID = " & SafeSQL(categoryId)
                ExecuteSQL sql
                message = "Kategorie byla úspěšně upravena."
                action = ""
        End Select
    Else
        message = "Název kategorie je povinný."
    End If
End If

' Zpracování akcí
If Request.Form("delete_action") <> "" Then
    categoryId = Request.Form("categoryId")
    ExecuteSQL "UPDATE Categories SET IsActive = 0 WHERE CategoryID = " & SafeSQL(categoryId)
    message = "Kategorie byla deaktivována."
ElseIf Request.Form("activate_action") <> "" Then
    categoryId = Request.Form("categoryId")
    ExecuteSQL "UPDATE Categories SET IsActive = 1 WHERE CategoryID = " & SafeSQL(categoryId)
    message = "Kategorie byla aktivována."
End If

' Získání kategorií
Dim rs
Set rs = GetRecordset("SELECT * FROM Categories ORDER BY Name")

' Získání kategorie pro editaci
Dim editCategory
If action = "edit" And Request.QueryString("id") <> "" Then
    Set editCategory = GetRecordset("SELECT * FROM Categories WHERE CategoryID = " & SafeSQL(Request.QueryString("id")))
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Správa kategorií - DINCO Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        border: 2px solid #292d31;
    }
    
    .admin-nav {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .admin-nav a {
        background: #007BFF;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .admin-nav a:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .admin-nav a.active {
        background: #0056b3;
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .form-container {
        background: #2A2A2A;
        padding: 25px;
        border-radius: 8px;
        border: 2px solid #292d31;
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #DDDDDD;
        font-weight: 500;
    }
    
    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #444444;
        border-radius: 5px;
        background-color: #333333;
        color: #FFFFFF;
        font-size: 16px;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #007BFF;
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }
    
    .categories-table {
        width: 100%;
        background: #2A2A2A;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #292d31;
    }
    
    .categories-table th,
    .categories-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #444;
        color: #DDDDDD;
    }
    
    .categories-table th {
        background: #333;
        color: #FFFFFF;
        font-weight: bold;
    }
    
    .categories-table tr:hover {
        background: #333;
    }
    
    .status-active {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-inactive {
        color: #dc3545;
        font-weight: bold;
    }
    
    .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }
    
    .action-buttons button,
    .action-buttons a {
        padding: 5px 10px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-edit {
        background: #007BFF;
        color: white;
    }
    
    .btn-edit:hover {
        background: #0056b3;
    }
    
    .btn-delete {
        background: #dc3545;
        color: white;
    }
    
    .btn-delete:hover {
        background: #c82333;
    }
    
    .btn-activate {
        background: #28a745;
        color: white;
    }
    
    .btn-activate:hover {
        background: #218838;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        background: #28a745;
        color: white;
        border: 1px solid #1e7e34;
    }
    
    @media (max-width: 768px) {
        .admin-nav {
            flex-direction: column;
        }
        
        .admin-nav a {
            text-align: center;
        }
        
        .categories-table {
            font-size: 14px;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>DINCO E-shop - Administrace</h1>
            <div class="admin-nav">
                <a href="dashboard.asp">Dashboard</a>
                <a href="products.asp">Produkty</a>
                <a href="categories.asp" class="active">Kategorie</a>
                <a href="orders.asp">Objednávky</a>
                <a href="import.asp">Import dat</a>
                <a href="../default.asp">Zpět na e-shop</a>
            </div>
        </div>

        <% If message <> "" Then %>
            <div class="message"><%=message%></div>
        <% End If %>

        <!-- Formulář pro přidání/editaci kategorie -->
        <div class="form-container">
            <h3><% If action = "edit" Then %>Upravit kategorii<% Else %>Přidat novou kategorii<% End If %></h3>
            
            <form method="post">
                <input type="hidden" name="action" value="<% If action = "edit" Then %>edit<% Else %>add<% End If %>">
                <% If action = "edit" And Not editCategory.EOF Then %>
                    <input type="hidden" name="categoryId" value="<%=editCategory("CategoryID")%>">
                <% End If %>
                
                <div class="form-group">
                    <label for="name">Název kategorie *</label>
                    <input type="text" id="name" name="name" value="<% If action = "edit" And Not editCategory.EOF Then %><%=editCategory("Name")%><% End If %>" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Popis</label>
                    <textarea id="description" name="description"><% If action = "edit" And Not editCategory.EOF Then %><%=editCategory("Description")%><% End If %></textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <% If action = "edit" Then %>Upravit kategorii<% Else %>Přidat kategorii<% End If %>
                </button>
                
                <% If action = "edit" Then %>
                    <a href="categories.asp" class="btn btn-secondary">Zrušit</a>
                <% End If %>
            </form>
        </div>

        <!-- Seznam kategorií -->
        <div class="admin-content">
            <h3>Seznam kategorií</h3>
            
            <table class="categories-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Název</th>
                        <th>Popis</th>
                        <th>SEO URL</th>
                        <th>Produkty</th>
                        <th>Stav</th>
                        <th>Akce</th>
                    </tr>
                </thead>
                <tbody>
                    <% While Not rs.EOF %>
                    <tr>
                        <td><%=rs("CategoryID")%></td>
                        <td><%=rs("Name")%></td>
                        <td><%=Left(rs("Description"), 50)%><% If Len(rs("Description")) > 50 Then %>...<% End If %></td>
                        <td><%=rs("SEOUrl")%></td>
                        <td><%=GetSingleValue("SELECT COUNT(*) FROM Products WHERE CategoryID = " & rs("CategoryID") & " AND IsActive = 1")%></td>
                        <td>
                            <% If rs("IsActive") Then %>
                                <span class="status-active">Aktivní</span>
                            <% Else %>
                                <span class="status-inactive">Neaktivní</span>
                            <% End If %>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="categories.asp?action=edit&id=<%=rs("CategoryID")%>" class="btn-edit">Upravit</a>
                                <% If rs("IsActive") Then %>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="delete_action" value="1">
                                        <input type="hidden" name="categoryId" value="<%=rs("CategoryID")%>">
                                        <button type="submit" class="btn-delete" onclick="return confirm('Opravdu chcete deaktivovat tuto kategorii?')">Deaktivovat</button>
                                    </form>
                                <% Else %>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="activate_action" value="1">
                                        <input type="hidden" name="categoryId" value="<%=rs("CategoryID")%>">
                                        <button type="submit" class="btn-activate">Aktivovat</button>
                                    </form>
                                <% End If %>
                            </div>
                        </td>
                    </tr>
                    <% rs.MoveNext %>
                    <% Wend %>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>

<%
rs.Close
Set rs = Nothing
If action = "edit" And Not editCategory Is Nothing Then
    If Not editCategory.EOF Then editCategory.Close
    Set editCategory = Nothing
End If
%>
