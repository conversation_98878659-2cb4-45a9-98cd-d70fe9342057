<%@ CodePage=65001 %>
<!--#include file="includes/database.asp"-->
<%
Dim conn, rs
Set conn = DBConnect()
Set rs = Server.CreateObject("ADODB.Recordset")

' SEO optimalizace
Dim pageTitle, metaDesc
pageTitle = "Náš E-shop - Kvalitní produkty za skvělé ceny"
metaDesc = "Široká nabídka kvalitních produktů za skvělé ceny. Doručení do 24 hodin, osobní odběr zdarma."
%>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <title><%=pageTitle%></title>
    <meta name="description" content="<%=metaDesc%>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!--#include file="includes/header.asp"-->
    <div class="hero-section">
        <div class="hero-content">
            <h1>Vítejte v našem e-shopu!</h1>
            <p class="hero-subtitle">Široký sortiment nerezových šroubů, matic, podložek a speciálních prvků třídy A2 a A4 skladem. Spolehněte se na naše odborné znalosti a rychlé dodání.</p>
            <div class="hero-buttons">
                <a href="catalog.asp" class="btn btn-primary">Prohlédnout katalog</a>
                <a href="../rychla-poptavka.html" class="btn btn-secondary">Rychlá Poptávka</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Akční nabídky -->
        <section class="featured-section">
            <h2>Akční nabídky</h2>
            <div class="product-grid">
                <% 
                sql = "SELECT TOP 4 ProductID, Name, Price, DiscountPrice, MainImage, SEOUrl " & _
                      "FROM Products " & _
                      "WHERE IsActive = 1 AND DiscountPrice IS NOT NULL " & _
                      "ORDER BY (Price - DiscountPrice) DESC"
                rs.Open sql, conn
                
                While Not rs.EOF
                    Dim discount: discount = Round((1 - rs("DiscountPrice")/rs("Price")) * 100)
                %>
                    <div class="product-card">
                        <div class="discount-badge">-<%=discount%>%</div>
                        <% If rs("MainImage") <> "" Then %>
                            <img src="<%=PRODUCT_IMAGES_PATH & rs("MainImage")%>" alt="<%=rs("Name")%>">
                        <% End If %>
                        <h3><%=rs("Name")%></h3>
                        <div class="price-container">
                            <span class="original-price"><%=FormatCurrency(rs("Price"))%></span>
                            <span class="discount-price"><%=FormatCurrency(rs("DiscountPrice"))%></span>
                        </div>
                        <div class="product-actions">
                            <button onclick="cart.addItem(<%=rs("ProductID")%>, 1)" class="btn">Do košíku</button>
                            <a href="product.asp?url=<%=rs("SEOUrl")%>" class="btn btn-secondary">Detail</a>
                        </div>
                    </div>
                <%
                    rs.MoveNext
                Wend
                rs.Close
                %>
            </div>
        </section>

        <!-- Populární kategorie -->
        <section class="categories-section">
            <h2>Oblíbené kategorie</h2>
            <div class="category-grid">
                <%
                sql = "SELECT TOP 6 c.CategoryID, c.Name, c.SEOUrl, " & _
                      "COUNT(p.ProductID) as ProductCount, " & _
                      "(SELECT TOP 1 MainImage FROM Products " & _
                      "WHERE CategoryID = c.CategoryID AND MainImage IS NOT NULL) as CategoryImage " & _
                      "FROM Categories c " & _
                      "LEFT JOIN Products p ON c.CategoryID = p.CategoryID " & _
                      "WHERE c.IsActive = 1 " & _
                      "GROUP BY c.CategoryID, c.Name, c.SEOUrl " & _
                      "ORDER BY ProductCount DESC"
                rs.Open sql, conn
                
                While Not rs.EOF
                %>
                    <a href="catalog.asp?category=<%=rs("CategoryID")%>" class="category-card">
                        <% If rs("CategoryImage") <> "" Then %>
                            <img src="<%=PRODUCT_IMAGES_PATH & rs("CategoryImage")%>" alt="<%=rs("Name")%>">
                        <% End If %>
                        <h3><%=rs("Name")%></h3>
                        <span class="product-count"><%=rs("ProductCount")%> produktů</span>
                    </a>
                <%
                    rs.MoveNext
                Wend
                rs.Close
                %>
            </div>
        </section>

        <!-- Nejnovější produkty -->
        <section class="new-products-section">
            <h2>Nejnovější přírůstky</h2>
            <div class="product-grid">
                <% 
                sql = "SELECT TOP 8 p.*, c.Name AS CategoryName " & _
                      "FROM Products p " & _
                      "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                      "WHERE p.IsActive = 1 " & _
                      "ORDER BY p.Created DESC"
                rs.Open sql, conn
                
                While Not rs.EOF
                %>
                    <div class="product-card">
                        <% If rs("MainImage") <> "" Then %>
                            <img src="<%=PRODUCT_IMAGES_PATH & rs("MainImage")%>" alt="<%=rs("Name")%>">
                        <% End If %>
                        <span class="category-tag"><%=rs("CategoryName")%></span>
                        <h3><%=rs("Name")%></h3>
                        <p class="price"><%=FormatCurrency(rs("Price"))%></p>
                        <div class="product-actions">
                            <% If rs("Stock") > 0 Then %>
                                <button onclick="cart.addItem(<%=rs("ProductID")%>, 1)" class="btn">Do košíku</button>
                            <% Else %>
                                <span class="out-of-stock">Není skladem</span>
                            <% End If %>
                            <a href="product.asp?url=<%=rs("SEOUrl")%>" class="btn btn-secondary">Detail</a>
                        </div>
                    </div>
                <%
                    rs.MoveNext
                Wend
                rs.Close
                %>
            </div>
        </section>

        <!-- Výhody nákupu -->
        <section class="benefits-section">
            <h2>Proč nakupovat u nás</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <i class="icon-delivery"></i>
                    <h3>Rychlé doručení</h3>
                    <p>Doručení do 24 hodin po celé ČR</p>
                </div>
                <div class="benefit-card">
                    <i class="icon-return"></i>
                    <h3>Vrácení zdarma</h3>
                    <p>30 dní na vrácení zboží</p>
                </div>
                <div class="benefit-card">
                    <i class="icon-secure"></i>
                    <h3>Bezpečný nákup</h3>
                    <p>Zabezpečená platba a osobní údaje</p>
                </div>
                <div class="benefit-card">
                    <i class="icon-support"></i>
                    <h3>Zákaznická podpora</h3>
                    <p>Podpora 7 dní v týdnu</p>
                </div>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container footer-grid">
            <div class="footer-col">
                <h4>DINCO FASTENNERS</h4>
                <p>Váš spolehlivý dodavatel kvalitního nerezového spojovacího materiálu.</p>
            </div>
            <div class="footer-col">
                <h4>Rychlé odkazy</h4>
                <ul>
                    <li><a href="default.asp">Domů</a></li>
                    <li><a href="catalog.asp">Katalog</a></li>
                    <li><a href="cart.asp">Košík</a></li>
                    <li><a href="login.asp">Přihlášení</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4>Kontakt</h4>
                <p>Ulice 123<br>Město, PSČ<br> Telefon: +420 123 456 789<br> Email: <EMAIL></p>
            </div>
            <div class="footer-col">
                <h4>Sledujte nás</h4>
                <p>IČO: 1<br>DIČ: CZ12345678</p>
            </div>
        </div>
        <div class="container footer-bottom">
            <p>&copy; 2025 DINCO FASTENNERS. Všechna práva vyhrazena.</p>
        </div>
    </footer>

    <script src="js/cart.js"></script>
    <script>
        // Hamburger menu functionality
        const menuToggle = document.getElementById('menuToggle');
        const mainNav = document.getElementById('mainNav');
        const menuOverlay = document.getElementById('menuOverlay');

        function toggleMenu() {
            menuToggle.classList.toggle('active');
            mainNav.classList.toggle('active');
            menuOverlay.classList.toggle('active');

            if (mainNav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMenu() {
            menuToggle.classList.remove('active');
            mainNav.classList.remove('active');
            menuOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        if (menuToggle) {
            menuToggle.addEventListener('click', toggleMenu);
        }

        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMenu();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMenu();
            }
        });
    </script>
</body>
</html>
<%

CloseDB(conn)
%>
