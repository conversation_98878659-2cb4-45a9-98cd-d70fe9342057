<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/session.asp"-->
<%
CheckSession()
If Not Session("IsAdmin") Then Response.Redirect "../login.asp"

Dim action
action = Request.QueryString("action")

' Zpracování formuláře
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Select Case action
        Case "add", "edit"
            Dim productId, name, description, price, categoryId, stock, seoUrl
            
            name = Request.Form("name")
            description = Request.Form("description")
            price = Request.Form("price")
            categoryId = Request.Form("categoryId")
            stock = Request.Form("stock")
            seoUrl = CreateSEOUrl(name)
            
            If action = "add" Then
                sql = "INSERT INTO Products (Name, Description, Price, CategoryID, Stock, SEOUrl) " & _
                      "VALUES (" & SafeSQL(name) & ", " & SafeSQL(description) & ", " & _
                      SafeSQL(price) & ", " & SafeSQL(categoryId) & ", " & SafeSQL(stock) & ", " & _
                      SafeSQL(seoUrl) & ")"
            Else
                productId = Request.Form("productId")
                sql = "UPDATE Products SET " & _
                      "Name = " & SafeSQL(name) & ", " & _
                      "Description = " & SafeSQL(description) & ", " & _
                      "Price = " & SafeSQL(price) & ", " & _
                      "CategoryID = " & SafeSQL(categoryId) & ", " & _
                      "Stock = " & SafeSQL(stock) & ", " & _
                      "SEOUrl = " & SafeSQL(seoUrl) & ", " & _
                      "Modified = GETDATE() " & _
                      "WHERE ProductID = " & SafeSQL(productId)
            End If
            
            ExecuteSQL sql
            
            ' Zpracování obrázku
            If Request.Form("image") <> "" Then
                ProcessProductImage productId, Request.Form("image")
            End If
            
            Response.Redirect "products.asp"
            
        Case "delete"
            productId = Request.Form("productId")
            sql = "DELETE FROM Products WHERE ProductID = " & SafeSQL(productId)
            ExecuteSQL sql
            Response.Redirect "products.asp"
    End Select
End If
%>

<!DOCTYPE html>
<html>
<head>
    <title>Správa produktů - Administrace</title>
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <h1>Správa produktů</h1>
        
        <div class="actions">
            <a href="?action=add" class="btn">Přidat produkt</a>
        </div>

        <% If action = "add" Or action = "edit" Then %>
            <form method="post" enctype="multipart/form-data">
                <% 
                If action = "edit" Then
                    productId = Request.QueryString("id")
                    Set product = GetProduct(productId)
                End If
                %>
                <div class="form-group">
                    <label>Název:</label>
                    <input type="text" name="name" value="<%=IIf(action="edit", product("Name"), "")%>" required>
                </div>
                
                <div class="form-group">
                    <label>Popis:</label>
                    <textarea name="description"><%=IIf(action="edit", product("Description"), "")%></textarea>
                </div>
                
                <div class="form-group">
                    <label>Cena:</label>
                    <input type="number" step="0.01" name="price" value="<%=IIf(action="edit", product("Price"), "")%>" required>
                </div>
                
                <div class="form-group">
                    <label>Kategorie:</label>
                    <select name="categoryId" required>
                        <% 
                        Set rs = GetRecordset("SELECT CategoryID, Name FROM Categories ORDER BY Name")
                        While Not rs.EOF
                            selected = ""
                            If action = "edit" And CStr(rs("CategoryID")) = CStr(product("CategoryID")) Then
                                selected = " selected"
                            End If
                        %>
                            <option value="<%=rs("CategoryID")%>"<%=selected%>><%=rs("Name")%></option>
                        <%
                            rs.MoveNext
                        Wend
                        rs.Close
                        %>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Skladem:</label>
                    <input type="number" name="stock" value="<%=IIf(action="edit", product("Stock"), "0")%>" required>
                </div>
                
                <div class="form-group">
                    <label>Obrázek:</label>
                    <input type="file" name="image">
                </div>
                
                <% If action = "edit" Then %>
                    <input type="hidden" name="productId" value="<%=productId%>">
                <% End If %>
                
                <button type="submit" class="btn">Uložit</button>
            </form>
        <% Else %>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Název</th>
                        <th>Kategorie</th>
                        <th>Cena</th>
                        <th>Skladem</th>
                        <th>Akce</th>
                    </tr>
                </thead>
                <tbody>
                    <%
                    sql = "SELECT p.*, c.Name AS CategoryName " & _
                          "FROM Products p " & _
                          "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                          "ORDER BY p.Created DESC"
                    
                    Set rs = GetRecordset(sql)
                    While Not rs.EOF
                    %>
                        <tr>
                            <td><%=rs("ProductID")%></td>
                            <td><%=rs("Name")%></td>
                            <td><%=rs("CategoryName")%></td>
                            <td><%=FormatCurrency(rs("Price"))%></td>
                            <td><%=rs("Stock")%></td>
                            <td>
                                <a href="?action=edit&id=<%=rs("ProductID")%>" class="btn-small">Upravit</a>
                                <form method="post" style="display:inline" onsubmit="return confirm('Opravdu smazat?')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="productId" value="<%=rs("ProductID")%>">
                                    <button type="submit" class="btn-small btn-danger">Smazat</button>
                                </form>
                            </td>
                        </tr>
                    <%
                        rs.MoveNext
                    Wend
                    rs.Close
                    %>
                </tbody>
            </table>
        <% End If %>
    </div>
</body>
</html>