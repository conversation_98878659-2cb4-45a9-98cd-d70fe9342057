-- Kategorie produktů
CREATE TABLE Categories (
    CategoryID INT IDENTITY(1,1) PRIMARY KEY,
    Name VARCHAR(100),
    Description TEXT,
    ParentCategoryID INT,
    SEOUrl VARCHAR(100),
    IsActive BIT DEFAULT 1,
    OrderIndex INT,
    FOREIGN KEY (ParentCategoryID) REFERENCES Categories(CategoryID)
)

-- Produkty
CREATE TABLE Products (
    ProductID INT IDENTITY(1,1) PRIMARY KEY,
    Name VARCHAR(100),
    Description TEXT,
    Price DECIMAL(10,2),
    DiscountPrice DECIMAL(10,2),
    Stock INT,
    CategoryID INT,
    SEOUrl VARCHAR(100) UNIQUE,
    MainImage VARCHAR(255),
    Weight DECIMAL(10,2),
    SKU VARCHAR(50) UNIQUE,
    IsActive BIT DEFAULT 1,
    IsFeatured BIT DEFAULT 0,
    Created DATETIME DEFAULT GETDATE(),
    Modified DATETIME,
    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)
)

-- Produktové obr<PERSON>zky
CREATE TABLE ProductImages (
    ImageID INT IDENTITY(1,1) PRIMARY KEY,
    ProductID INT,
    ImagePath VARCHAR(255),
    OrderIndex INT,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
)

-- Produktové parametry
CREATE TABLE ProductParameters (
    ParameterID INT IDENTITY(1,1) PRIMARY KEY,
    ProductID INT,
    Name VARCHAR(50),
    Value VARCHAR(255),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
)

-- Uživatelé
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Email VARCHAR(100) UNIQUE,
    Password VARCHAR(255),
    FirstName VARCHAR(50),
    LastName VARCHAR(50),
    Phone VARCHAR(20),
    IsAdmin BIT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    LastLogin DATETIME,
    Created DATETIME DEFAULT GETDATE(),
    Modified DATETIME
)

-- Adresy uživatelů
CREATE TABLE UserAddresses (
    AddressID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT,
    AddressType VARCHAR(20), -- 'BILLING' nebo 'SHIPPING'
    Street VARCHAR(100),
    City VARCHAR(50),
    PostalCode VARCHAR(10),
    Country VARCHAR(50),
    IsDefault BIT DEFAULT 0,
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
)

-- Objednávky
CREATE TABLE Orders (
    OrderID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT,
    OrderNumber VARCHAR(20) UNIQUE,
    Status VARCHAR(20), -- 'NEW', 'PAID', 'SHIPPED', 'COMPLETED', 'CANCELLED'
    TotalPrice DECIMAL(10,2),
    ShippingPrice DECIMAL(10,2),
    PaymentMethod VARCHAR(50),
    ShippingMethod VARCHAR(50),
    BillingAddressID INT,
    ShippingAddressID INT,
    Note TEXT,
    Created DATETIME DEFAULT GETDATE(),
    Modified DATETIME,
    FOREIGN KEY (UserID) REFERENCES Users(UserID),
    FOREIGN KEY (BillingAddressID) REFERENCES UserAddresses(AddressID),
    FOREIGN KEY (ShippingAddressID) REFERENCES UserAddresses(AddressID)
)

-- Položky objednávky
CREATE TABLE OrderItems (
    OrderItemID INT IDENTITY(1,1) PRIMARY KEY,
    OrderID INT,
    ProductID INT,
    Quantity INT,
    Price DECIMAL(10,2),
    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
)

-- Historie stavů objednávek
CREATE TABLE OrderHistory (
    HistoryID INT IDENTITY(1,1) PRIMARY KEY,
    OrderID INT,
    Status VARCHAR(20),
    Note TEXT,
    Created DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)
)

-- Produktová doporučení (AI)
CREATE TABLE ProductRecommendations (
    RecommendationID INT IDENTITY(1,1) PRIMARY KEY,
    SourceProductID INT,
    RecommendedProductID INT,
    Score DECIMAL(5,2),
    Created DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (SourceProductID) REFERENCES Products(ProductID),
    FOREIGN KEY (RecommendedProductID) REFERENCES Products(ProductID)
)

-- Nastavení webu
CREATE TABLE Settings (
    SettingID INT IDENTITY(1,1) PRIMARY KEY,
    [Key] VARCHAR(50) UNIQUE,
    Value TEXT,
    Description VARCHAR(255),
    Modified DATETIME
)

-- Indexy pro optimalizaci
CREATE INDEX IX_Products_CategoryID ON Products(CategoryID)
CREATE INDEX IX_Products_SEOUrl ON Products(SEOUrl)
CREATE INDEX IX_Orders_UserID ON Orders(UserID)
CREATE INDEX IX_Orders_OrderNumber ON Orders(OrderNumber)
CREATE INDEX IX_OrderItems_OrderID ON OrderItems(OrderID)
CREATE INDEX IX_UserAddresses_UserID ON UserAddresses(UserID)

-- Uložené košíky
CREATE TABLE SavedCarts (
    CartID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT,
    Name VARCHAR(100),
    Created DATETIME DEFAULT GETDATE(),
    LastModified DATETIME,
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
)

-- Položky uložených košíků
CREATE TABLE SavedCartItems (
    CartItemID INT IDENTITY(1,1) PRIMARY KEY,
    CartID INT,
    ProductID INT,
    Quantity INT,
    FOREIGN KEY (CartID) REFERENCES SavedCarts(CartID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
)

-- Indexy
CREATE INDEX IX_SavedCarts_UserID ON SavedCarts(UserID)
CREATE INDEX IX_SavedCartItems_CartID ON SavedCartItems(CartID)

