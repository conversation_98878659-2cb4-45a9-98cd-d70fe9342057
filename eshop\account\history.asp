<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/session.asp"-->
<%
CheckSession()

Dim userId: userId = Session("UserID")
%>

<!DOCTYPE html>
<html>
<head>
    <title>Historie objednávek - <%=SITE_NAME%></title>
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Moje historie</h1>
        
        <!-- Historie objednávek -->
        <section class="orders-history">
            <h2>Historie objednávek</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th><PERSON><PERSON><PERSON> objednávky</th>
                        <th>Datum</th>
                        <th>Stav</th>
                        <th><PERSON><PERSON><PERSON> cena</th>
                        <th>Akce</th>
                    </tr>
                </thead>
                <tbody>
                    <%
                    Dim ordersSql
                    ordersSql = "SELECT OrderID, OrderNumber, Created, Status, TotalPrice " & _
                               "FROM Orders WHERE UserID = " & SafeSQL(userId) & " " & _
                               "ORDER BY Created DESC"
                    
                    Set rs = GetRecordset(ordersSql)
                    While Not rs.EOF
                    %>
                        <tr>
                            <td><%=rs("OrderNumber")%></td>
                            <td><%=FormatDateTime(rs("Created"), 2)%></td>
                            <td><%=GetOrderStatusText(rs("Status"))%></td>
                            <td><%=FormatCurrency(rs("TotalPrice"))%></td>
                            <td>
                                <a href="order-detail.asp?id=<%=rs("OrderID")%>" class="btn-small">Detail</a>
                                <button onclick="reorderItems(<%=rs("OrderID")%>)" class="btn-small">Objednat znovu</button>
                            </td>
                        </tr>
                    <%
                        rs.MoveNext
                    Wend
                    rs.Close
                    %>
                </tbody>
            </table>
        </section>

        <!-- Uložené košíky -->
        <section class="saved-carts">
            <h2>Uložené košíky</h2>
            <div class="saved-carts-grid">
                <%
                Dim cartsSql
                cartsSql = "SELECT sc.CartID, sc.Name, sc.Created, " & _
                          "COUNT(sci.ProductID) as ItemCount, " & _
                          "SUM(p.Price * sci.Quantity) as TotalPrice " & _
                          "FROM SavedCarts sc " & _
                          "LEFT JOIN SavedCartItems sci ON sc.CartID = sci.CartID " & _
                          "LEFT JOIN Products p ON sci.ProductID = p.ProductID " & _
                          "WHERE sc.UserID = " & SafeSQL(userId) & " AND sc.IsActive = 1 " & _
                          "GROUP BY sc.CartID, sc.Name, sc.Created " & _
                          "ORDER BY sc.Created DESC"
                
                Set rs = GetRecordset(cartsSql)
                While Not rs.EOF
                %>
                    <div class="saved-cart-card">
                        <h3><%=rs("Name")%></h3>
                        <p>Vytvořeno: <%=FormatDateTime(rs("Created"), 2)%></p>
                        <p>Počet položek: <%=rs("ItemCount")%></p>
                        <p>Celková cena: <%=FormatCurrency(rs("TotalPrice"))%></p>
                        <div class="cart-actions">
                            <button onclick="loadSavedCart(<%=rs("CartID")%>)" class="btn-small">Načíst do košíku</button>
                            <button onclick="deleteSavedCart(<%=rs("CartID")%>)" class="btn-small btn-danger">Smazat</button>
                        </div>
                    </div>
                <%
                    rs.MoveNext
                Wend
                rs.Close
                %>
            </div>
        </section>
    </div>

    <script>
    function reorderItems(orderId) {
        fetch('api/reorder.asp?orderId=' + orderId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cart.loadFromStorage();
                    window.location.href = 'cart.asp';
                }
            });
    }

    function loadSavedCart(cartId) {
        if (confirm('Načtením uloženého košíku přepíšete současný obsah košíku. Chcete pokračovat?')) {
            fetch('api/load-saved-cart.asp?cartId=' + cartId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cart.loadFromStorage();
                        window.location.href = 'cart.asp';
                    }
                });
        }
    }

    function deleteSavedCart(cartId) {
        if (confirm('Opravdu chcete smazat tento uložený košík?')) {
            fetch('api/delete-saved-cart.asp?cartId=' + cartId, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                });
        }
    }
    </script>
</body>
</html>