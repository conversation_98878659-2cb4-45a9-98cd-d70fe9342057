<%@ CodePage=65001 %>
<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/security.asp"-->
<%
' Kontrola přihlášení administrátora
If Session("UserID") = "" Or Session("IsAdmin") <> "1" Then
    Response.Redirect "../login.asp?returnUrl=" & Server.URLEncode(Request.ServerVariables("SCRIPT_NAME"))
End If

Response.CharSet = "utf-8"
Response.CodePage = 65001

' Získání statistik
Dim totalProducts, totalCategories, totalOrders, lowStock
totalProducts = GetSingleValue("SELECT COUNT(*) FROM Products WHERE IsActive = 1")
totalCategories = GetSingleValue("SELECT COUNT(*) FROM Categories WHERE IsActive = 1")
totalOrders = GetSingleValue("SELECT COUNT(*) FROM Orders")
lowStock = GetSingleValue("SELECT COUNT(*) FROM Products WHERE Stock < 10 AND IsActive = 1")
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Dashboard - DINCO Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        border: 2px solid #292d31;
    }
    
    .admin-nav {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .admin-nav a {
        background: #007BFF;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .admin-nav a:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .admin-nav a.active {
        background: #0056b3;
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #2A2A2A;
        padding: 25px;
        border-radius: 8px;
        border: 2px solid #292d31;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
        border-color: #d0e3f7;
    }
    
    .stat-number {
        font-size: 3rem;
        font-weight: bold;
        color: #007BFF;
        margin-bottom: 10px;
        font-family: 'Oswald', sans-serif;
    }
    
    .stat-label {
        color: #DDDDDD;
        font-size: 1.1rem;
        margin-bottom: 5px;
    }
    
    .stat-description {
        color: #BBBBBB;
        font-size: 0.9rem;
    }
    
    .quick-actions {
        background: #2A2A2A;
        padding: 25px;
        border-radius: 8px;
        border: 2px solid #292d31;
        margin-bottom: 30px;
    }
    
    .quick-actions h3 {
        color: #FFFFFF;
        margin-bottom: 20px;
    }
    
    .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .action-btn {
        background: #007BFF;
        color: white;
        padding: 15px 20px;
        text-decoration: none;
        border-radius: 5px;
        text-align: center;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .action-btn:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: #6c757d;
    }
    
    .action-btn.secondary:hover {
        background: #545b62;
    }
    
    .action-btn.success {
        background: #28a745;
    }
    
    .action-btn.success:hover {
        background: #218838;
    }
    
    .recent-activity {
        background: #2A2A2A;
        padding: 25px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    .recent-activity h3 {
        color: #FFFFFF;
        margin-bottom: 20px;
    }
    
    .activity-item {
        padding: 10px 0;
        border-bottom: 1px solid #444;
        color: #DDDDDD;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-time {
        color: #BBBBBB;
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .admin-nav {
            flex-direction: column;
        }
        
        .admin-nav a {
            text-align: center;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            grid-template-columns: 1fr;
        }
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>DINCO E-shop - Administrace</h1>
            <div class="admin-nav">
                <a href="dashboard.asp" class="active">Dashboard</a>
                <a href="products.asp">Produkty</a>
                <a href="categories.asp">Kategorie</a>
                <a href="orders.asp">Objednávky</a>
                <a href="import.asp">Import dat</a>
                <a href="../default.asp">Zpět na e-shop</a>
                <a href="../logout.asp">Odhlásit se</a>
            </div>
        </div>

        <!-- Statistiky -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><%=totalProducts%></div>
                <div class="stat-label">Aktivní produkty</div>
                <div class="stat-description">v e-shopu</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%=totalCategories%></div>
                <div class="stat-label">Kategorie</div>
                <div class="stat-description">produktů</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%=totalOrders%></div>
                <div class="stat-label">Objednávky</div>
                <div class="stat-description">celkem</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: <%=IIf(lowStock > 0, "#dc3545", "#28a745")%>"><%=lowStock%></div>
                <div class="stat-label">Nízký sklad</div>
                <div class="stat-description">produktů</div>
            </div>
        </div>

        <!-- Rychlé akce -->
        <div class="quick-actions">
            <h3>Rychlé akce</h3>
            <div class="action-buttons">
                <a href="product-edit.asp" class="action-btn">Přidat produkt</a>
                <a href="categories.asp?action=add" class="action-btn secondary">Přidat kategorii</a>
                <a href="import.asp" class="action-btn success">Import dat</a>
                <a href="export.asp" class="action-btn secondary">Export dat</a>
            </div>
        </div>

        <!-- Nedávná aktivita -->
        <div class="recent-activity">
            <h3>Nedávná aktivita</h3>
            <%
            Dim activitySql, activityRs
            activitySql = "SELECT TOP 5 p.Name, p.Created, 'Produkt' as Type " & _
                         "FROM Products p " & _
                         "WHERE p.Created >= DATEADD(day, -7, GETDATE()) " & _
                         "UNION ALL " & _
                         "SELECT TOP 5 c.Name, c.Created, 'Kategorie' as Type " & _
                         "FROM Categories c " & _
                         "WHERE c.Created >= DATEADD(day, -7, GETDATE()) " & _
                         "ORDER BY Created DESC"
            
            Set activityRs = GetRecordset(activitySql)
            
            If Not activityRs.EOF Then
                While Not activityRs.EOF
            %>
                <div class="activity-item">
                    <strong><%=activityRs("Type")%>:</strong> <%=activityRs("Name")%>
                    <div class="activity-time"><%=FormatDateTime(activityRs("Created"), 0)%></div>
                </div>
            <%
                    activityRs.MoveNext
                Wend
            Else
            %>
                <div class="activity-item">
                    Žádná nedávná aktivita
                </div>
            <%
            End If
            activityRs.Close
            Set activityRs = Nothing
            %>
        </div>
    </div>
</body>
</html>
