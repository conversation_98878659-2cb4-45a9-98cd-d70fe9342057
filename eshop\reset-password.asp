<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<!--#include file="includes/security.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim errorMessage, successMessage, email, token, showResetForm

' Kontrola, zda jde o stránku pro zadání nového hesla (s tokenem)
token = Request.QueryString("token")
showResetForm = (token <> "")

' Funkce pro generování náhodného <PERSON> (token pro reset hesla)
Function GenerateRandomString(length)
    Dim chars, i, randomString
    chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    Randomize
    randomString = ""
    For i = 1 To length
        randomString = randomString & Mid(chars, Int(Rnd * Len(chars)) + 1, 1)
    Next
    GenerateRandomString = randomString
End Function

' <PERSON>pracování odesl<PERSON> formul<PERSON>ře pro reset hesla (první krok)
If Request.ServerVariables("REQUEST_METHOD") = "POST" And Not showResetForm Then
    email = Trim(Request.Form("email"))
    
    If email <> "" Then
        ' Kontrola, zda email existuje v databázi
        Dim checkSql, checkRs
        checkSql = "SELECT UserID, Email, Name FROM Users WHERE Email = " & SafeSQL(email) & " AND IsActive = 1"
        Set checkRs = GetRecordset(checkSql)
        
        If Not checkRs.EOF Then
            ' Email existuje, generujeme token a ukládáme do databáze
            Dim userId, resetToken, expiryDate
            userId = checkRs("UserID")
            resetToken = GenerateRandomString(32) ' 32 znaků dlouhý token
            
            ' Token bude platný 24 hodin
            expiryDate = DateAdd("h", 24, Now())
            
            ' Uložíme token do databáze
            Dim updateSql
            updateSql = "UPDATE Users SET " & _
                        "PasswordResetToken = " & SafeSQL(resetToken) & ", " & _
                        "PasswordResetExpiry = " & SafeSQL(expiryDate) & " " & _
                        "WHERE UserID = " & userId
            
            ExecuteSQL updateSql
            
            ' Teoreticky bychom zde poslali email s odkazem na reset hesla
            ' Pro účely demonstrace pouze zobrazíme odkaz na stránce
            Dim resetLink
            resetLink = "http://" & Request.ServerVariables("SERVER_NAME") & Request.ServerVariables("URL") & "?token=" & Server.URLEncode(resetToken)
            
            successMessage = "Na váš email byl odeslán odkaz pro resetování hesla. Klikněte na něj pro nastavení nového hesla.<br><br>" & _
                             "Pro účely demonstrace: <a href='" & resetLink & "'>Odkaz pro reset hesla</a>"
        Else
            ' Email neexistuje, ale z bezpečnostních důvodů to neuvedeme
            successMessage = "Pokud je zadaný email registrován v našem systému, byl na něj odeslán odkaz pro resetování hesla."
        End If
        
        Set checkRs = Nothing
    Else
        errorMessage = "Zadejte prosím váš email."
    End If
End If

' Zpracování formuláře pro nastavení nového hesla (druhý krok)
If Request.ServerVariables("REQUEST_METHOD") = "POST" And showResetForm Then
    Dim newPassword, confirmPassword
    newPassword = Trim(Request.Form("newPassword"))
    confirmPassword = Trim(Request.Form("confirmPassword"))
    token = Trim(Request.Form("token"))
    
    If newPassword <> "" And confirmPassword <> "" Then
        If newPassword = confirmPassword Then
            If Len(newPassword) >= 6 Then
                ' Kontrola platnosti tokenu
                Dim tokenSql, tokenRs
                tokenSql = "SELECT UserID FROM Users WHERE PasswordResetToken = " & SafeSQL(token) & _
                           " AND PasswordResetExpiry > GETDATE() AND IsActive = 1"
                Set tokenRs = GetRecordset(tokenSql)
                
                If Not tokenRs.EOF Then
                    ' Token je platný, aktualizujeme heslo
                    Dim userIdForReset, hashedPassword
                    userIdForReset = tokenRs("UserID")
                    hashedPassword = HashPassword(newPassword)
                    
                    ' Aktualizace hesla a vymazání tokenu
                    Dim resetSql
                    resetSql = "UPDATE Users SET " & _
                               "Password = " & SafeSQL(hashedPassword) & ", " & _
                               "PasswordResetToken = NULL, " & _
                               "PasswordResetExpiry = NULL " & _
                               "WHERE UserID = " & userIdForReset
                    
                    ExecuteSQL resetSql
                    
                    ' Přesměrování na přihlášení s informací o úspěšné změně hesla
                    Response.Redirect "login.asp?passwordReset=1"
                Else
                    errorMessage = "Odkaz pro resetování hesla již není platný nebo vypršela jeho platnost. Požádejte prosím o nový odkaz."
                End If
                
                Set tokenRs = Nothing
            Else
                errorMessage = "Heslo musí mít alespoň 6 znaků."
            End If
        Else
            errorMessage = "Hesla se neshodují."
        End If
    Else
        errorMessage = "Vyplňte prosím nové heslo a jeho potvrzení."
    End If
End If

' Kontrola platnosti tokenu pro zobrazení formuláře pro reset hesla
If showResetForm Then
    Dim validateSql, validateRs
    validateSql = "SELECT UserID FROM Users WHERE PasswordResetToken = " & SafeSQL(token) & _
                  " AND PasswordResetExpiry > GETDATE() AND IsActive = 1"
    Set validateRs = GetRecordset(validateSql)
    
    If validateRs.EOF Then
        ' Token není platný nebo vypršel, zobrazíme chybu
        errorMessage = "Odkaz pro resetování hesla již není platný nebo vypršela jeho platnost. Požádejte prosím o nový odkaz."
        showResetForm = False
    End If
    
    Set validateRs = Nothing
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><% If showResetForm Then %>Nastavení nového hesla<% Else %>Zapomenuté heslo<% End If %> - <%=SITE_NAME%></title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!--#include file="includes/header.asp"-->
    
    <div class="container">
        <div class="auth-form">
            <% If showResetForm Then %>
                <h1>Nastavení nového hesla</h1>
            <% Else %>
                <h1>Zapomenuté heslo</h1>
            <% End If %>
            
            <% If errorMessage <> "" Then %>
                <div class="error-message"><%=errorMessage%></div>
            <% End If %>
            
            <% If successMessage <> "" Then %>
                <div class="success-message"><%=successMessage%></div>
            <% Else %>
                <% If showResetForm Then %>
                    <!-- Formulář pro nastavení nového hesla -->
                    <form method="post" action="reset-password.asp?token=<%=Server.URLEncode(token)%>">
                        <input type="hidden" name="token" value="<%=token%>">
                        
                        <div class="form-group">
                            <label for="newPassword">Nové heslo:</label>
                            <input type="password" id="newPassword" name="newPassword" required>
                            <span class="form-hint">Heslo musí mít alespoň 6 znaků</span>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">Potvrzení hesla:</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Nastavit nové heslo</button>
                        </div>
                    </form>
                <% Else %>
                    <!-- Formulář pro zadání emailu -->
                    <form method="post" action="reset-password.asp">
                        <div class="form-group">
                            <label for="email">Email:</label>
                            <input type="email" id="email" name="email" value="<%=Request.Form("email")%>" required>
                        </div>
                        
                        <div class="form-help">
                            <p>Zadejte svůj registrační email. Zašleme vám odkaz pro obnovu hesla.</p>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Odeslat odkaz pro reset hesla</button>
                            <a href="login.asp" class="btn btn-link">Zpět na přihlášení</a>
                        </div>
                    </form>
                <% End If %>
            <% End If %>
        </div>
    </div>

    <style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .auth-form {
        max-width: 500px;
        margin: 40px auto;
        padding: 30px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .auth-form h1 {
        margin-top: 0;
        color: #333;
        font-size: 24px;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #444;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        transition: border-color 0.2s;
    }
    
    .form-group input:focus {
        border-color: #4CAF50;
        outline: none;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    }
    
    .form-hint {
        display: block;
        margin-top: 5px;
        font-size: 14px;
        color: #666;
    }
    
    .form-help {
        margin-bottom: 20px;
        padding: 10px 15px;
        background-color: #f5f5f5;
        border-left: 4px solid #4CAF50;
        border-radius: 0 4px 4px 0;
    }
    
    .form-help p {
        margin: 0;
        color: #444;
        font-size: 14px;
    }
    
    .form-actions {
        margin-top: 30px;
        display: flex;
        align-items: center;
    }
    
    .btn {
        display: inline-block;
        padding: 12px 20px;
        background: #eee;
        border: none;
        border-radius: 4px;
        color: #333;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        transition: background 0.2s, transform 0.1s;
    }
    
    .btn:hover {
        background: #ddd;
    }
    
    .btn:active {
        transform: translateY(1px);
    }
    
    .btn-primary {
        background: #4CAF50;
        color: white;
    }
    
    .btn-primary:hover {
        background: #3d9140;
    }
    
    .btn-link {
        background: none;
        color: #4CAF50;
        padding: 10px 0 10px 15px;
        font-weight: normal;
    }
    
    .btn-link:hover {
        background: none;
        text-decoration: underline;
    }
    
    .error-message {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #ffebee;
        border-radius: 4px;
        color: #d32f2f;
        font-size: 15px;
    }
    
    .success-message {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #e8f5e9;
        border-radius: 4px;
        color: #2e7d32;
        font-size: 15px;
    }
    </style>
    
   
</body>
</html> 