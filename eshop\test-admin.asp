<%@ CodePage=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test admin práv - DINCO</title>
    <style>
    body {
        font-family: Arial, sans-serif;
        background-color: #1A1A1A;
        color: #DDDDDD;
        padding: 20px;
    }
    
    .test-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #2A2A2A;
        padding: 30px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    .test-result {
        padding: 15px;
        margin: 10px 0;
        border-radius: 5px;
    }
    
    .success {
        background-color: #28a745;
        color: white;
    }
    
    .error {
        background-color: #dc3545;
        color: white;
    }
    
    .info {
        background-color: #17a2b8;
        color: white;
    }
    
    h1 {
        color: #FFFFFF;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .user-info {
        background-color: #333333;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
    }
    
    .action-links {
        text-align: center;
        margin-top: 30px;
    }
    
    .action-links a {
        color: #007BFF;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 600;
    }
    
    .action-links a:hover {
        text-decoration: underline;
    }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test admin práv a session</h1>
        
        <div class="test-result info">
            <strong>Aktuální session informace:</strong><br>
            UserID: <%=Session("UserID")%><br>
            IsAdmin: <%=Session("IsAdmin")%><br>
            Session Timeout: <%=Session.Timeout%> minut
        </div>
        
        <% If Session("UserID") <> "" Then %>
            <div class="test-result success">
                ✓ Uživatel je přihlášen
            </div>
            
            <%
            ' Získání informací o uživateli
            Dim userRs, sql
            sql = "SELECT * FROM Users WHERE UserID = " & SafeSQL(Session("UserID"))
            Set userRs = GetRecordset(sql)
            
            If Not userRs.EOF Then
            %>
                <div class="user-info">
                    <strong>Informace o uživateli:</strong><br>
                    ID: <%=userRs("UserID")%><br>
                    Jméno: <%=userRs("FirstName")%> <%=userRs("LastName")%><br>
                    E-mail: <%=userRs("Email")%><br>
                    IsAdmin v DB: <%=userRs("IsAdmin")%><br>
                    IsActive: <%=userRs("IsActive")%><br>
                    Vytvořen: <%=userRs("Created")%>
                </div>
                
                <% If userRs("IsAdmin") = 1 Or userRs("IsAdmin") = "1" Then %>
                    <div class="test-result success">
                        ✓ Uživatel má admin práva v databázi
                    </div>
                    
                    <% If Session("IsAdmin") = "1" Or Session("IsAdmin") = 1 Then %>
                        <div class="test-result success">
                            ✓ Admin práva jsou správně nastavena v session
                        </div>
                    <% Else %>
                        <div class="test-result error">
                            ✗ Admin práva nejsou správně nastavena v session<br>
                            Session IsAdmin: "<%=Session("IsAdmin")%>"<br>
                            Typ: <%=TypeName(Session("IsAdmin"))%>
                        </div>
                    <% End If %>
                <% Else %>
                    <div class="test-result error">
                        ✗ Uživatel nemá admin práva v databázi
                    </div>
                <% End If %>
            <%
                userRs.Close
                Set userRs = Nothing
            Else
            %>
                <div class="test-result error">
                    ✗ Uživatel nebyl nalezen v databázi
                </div>
            <% End If %>
            
        <% Else %>
            <div class="test-result error">
                ✗ Uživatel není přihlášen
            </div>
        <% End If %>
        
        <!-- Test připojení k databázi -->
        <%
        Dim testQuery
        On Error Resume Next
        testQuery = GetSingleValue("SELECT COUNT(*) FROM Users")
        If Err.Number = 0 Then
        %>
            <div class="test-result success">
                ✓ Připojení k databázi funguje<br>
                Počet uživatelů v databázi: <%=testQuery%>
            </div>
        <% Else %>
            <div class="test-result error">
                ✗ Chyba připojení k databázi: <%=Err.Description%>
            </div>
        <% End If %>
        On Error Goto 0
        
        <!-- Test admin uživatelů -->
        <%
        Dim adminCount
        adminCount = GetSingleValue("SELECT COUNT(*) FROM Users WHERE IsAdmin = 1")
        %>
        <div class="test-result info">
            <strong>Počet admin uživatelů v databázi:</strong> <%=adminCount%>
        </div>
        
        <div class="action-links">
            <a href="login.asp">Přihlášení</a>
            <a href="create-admin.asp">Vytvořit admin</a>
            <a href="admin/dashboard.asp">Admin dashboard</a>
            <a href="account/dashboard.asp">Uživatelský dashboard</a>
            <a href="logout.asp">Odhlásit se</a>
        </div>
    </div>
</body>
</html>
