<%@ CodePage=65001 %>
<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/security.asp"-->
<%
' Kontrola přihlášení administrátora
If Session("UserID") = "" Or Session("IsAdmin") <> "1" Then
    Response.Redirect "../login.asp?returnUrl=" & Server.URLEncode(Request.ServerVariables("SCRIPT_NAME"))
End If

Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim message, importType
importType = Request.Form("importType")

' Zpracování CSV importu
If Request.ServerVariables("REQUEST_METHOD") = "POST" And Request.Form("csvData") <> "" Then
    Dim csvData, lines, i, fields
    csvData = Request.Form("csvData")
    lines = Split(csvData, vbCrLf)
    
    Dim successCount, errorCount
    successCount = 0
    errorCount = 0
    
    message = "<h4>Výsledky importu:</h4>"
    
    Select Case importType
        Case "categories"
            For i = 1 To UBound(lines) ' Přeskočit header
                If Trim(lines(i)) <> "" Then
                    fields = Split(lines(i), ";")
                    If UBound(fields) >= 1 Then
                        Dim catName, catDesc, catSEO
                        catName = Trim(fields(0))
                        catDesc = IIf(UBound(fields) >= 1, Trim(fields(1)), "")
                        catSEO = CreateSEOUrl(catName)
                        
                        If catName <> "" Then
                            On Error Resume Next
                            Dim sql
                            sql = "INSERT INTO Categories (Name, Description, SEOUrl, IsActive, Created) VALUES (" & _
                                  SafeSQL(catName) & ", " & SafeSQL(catDesc) & ", " & SafeSQL(catSEO) & ", 1, GETDATE())"
                            ExecuteSQL sql
                            
                            If Err.Number = 0 Then
                                successCount = successCount + 1
                            Else
                                errorCount = errorCount + 1
                                message = message & "<p style='color: red;'>Chyba při importu kategorie '" & catName & "': " & Err.Description & "</p>"
                            End If
                            On Error Goto 0
                        End If
                    End If
                End If
            Next
            
        Case "products"
            For i = 1 To UBound(lines) ' Přeskočit header
                If Trim(lines(i)) <> "" Then
                    fields = Split(lines(i), ";")
                    If UBound(fields) >= 3 Then
                        Dim prodName, prodDesc, prodPrice, prodCategoryID, prodStock, prodSEO
                        prodName = Trim(fields(0))
                        prodDesc = IIf(UBound(fields) >= 1, Trim(fields(1)), "")
                        prodPrice = IIf(UBound(fields) >= 2, Trim(fields(2)), "0")
                        prodCategoryID = IIf(UBound(fields) >= 3, Trim(fields(3)), "1")
                        prodStock = IIf(UBound(fields) >= 4, Trim(fields(4)), "0")
                        prodSEO = CreateSEOUrl(prodName)
                        
                        If prodName <> "" And IsNumeric(prodPrice) And IsNumeric(prodCategoryID) Then
                            On Error Resume Next
                            sql = "INSERT INTO Products (Name, Description, Price, CategoryID, Stock, SEOUrl, IsActive, Created) VALUES (" & _
                                  SafeSQL(prodName) & ", " & SafeSQL(prodDesc) & ", " & _
                                  SafeSQL(prodPrice) & ", " & SafeSQL(prodCategoryID) & ", " & _
                                  SafeSQL(prodStock) & ", " & SafeSQL(prodSEO) & ", 1, GETDATE())"
                            ExecuteSQL sql
                            
                            If Err.Number = 0 Then
                                successCount = successCount + 1
                            Else
                                errorCount = errorCount + 1
                                message = message & "<p style='color: red;'>Chyba při importu produktu '" & prodName & "': " & Err.Description & "</p>"
                            End If
                            On Error Goto 0
                        End If
                    End If
                End If
            Next
    End Select
    
    message = message & "<p style='color: green;'>Úspěšně importováno: " & successCount & " záznamů</p>"
    If errorCount > 0 Then
        message = message & "<p style='color: red;'>Chyby: " & errorCount & " záznamů</p>"
    End If
End If

' Získání kategorií pro dropdown
Dim categoriesRs
Set categoriesRs = GetRecordset("SELECT CategoryID, Name FROM Categories WHERE IsActive = 1 ORDER BY Name")
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Import dat - DINCO Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        border: 2px solid #292d31;
    }
    
    .admin-nav {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .admin-nav a {
        background: #007BFF;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .admin-nav a:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .admin-nav a.active {
        background: #0056b3;
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .import-section {
        background: #2A2A2A;
        padding: 25px;
        border-radius: 8px;
        border: 2px solid #292d31;
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #DDDDDD;
        font-weight: 500;
    }
    
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #444444;
        border-radius: 5px;
        background-color: #333333;
        color: #FFFFFF;
        font-size: 16px;
        transition: border-color 0.3s ease;
    }
    
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #007BFF;
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 200px;
        font-family: monospace;
    }
    
    .format-info {
        background: #333333;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        color: #DDDDDD;
        font-size: 14px;
    }
    
    .format-info h4 {
        color: #FFFFFF;
        margin-bottom: 10px;
    }
    
    .format-info code {
        background: #444444;
        padding: 2px 5px;
        border-radius: 3px;
        color: #00A1FF;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        background: #2A2A2A;
        border: 2px solid #292d31;
        color: #DDDDDD;
    }
    
    .quick-add {
        background: #333333;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .quick-add h4 {
        color: #FFFFFF;
        margin-bottom: 15px;
    }
    
    .quick-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .quick-btn {
        background: #007BFF;
        color: white;
        padding: 8px 15px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s ease;
    }
    
    .quick-btn:hover {
        background: #0056b3;
    }
    
    @media (max-width: 768px) {
        .admin-nav {
            flex-direction: column;
        }
        
        .admin-nav a {
            text-align: center;
        }
        
        .quick-buttons {
            flex-direction: column;
        }
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>DINCO E-shop - Administrace</h1>
            <div class="admin-nav">
                <a href="dashboard.asp">Dashboard</a>
                <a href="products.asp">Produkty</a>
                <a href="categories.asp">Kategorie</a>
                <a href="orders.asp">Objednávky</a>
                <a href="import.asp" class="active">Import dat</a>
                <a href="../default.asp">Zpět na e-shop</a>
            </div>
        </div>

        <% If message <> "" Then %>
            <div class="message"><%=message%></div>
        <% End If %>

        <!-- CSV Import -->
        <div class="import-section">
            <h3>Import dat z CSV</h3>
            
            <form method="post">
                <div class="form-group">
                    <label for="importType">Typ importu</label>
                    <select id="importType" name="importType" onchange="updateFormatInfo()" required>
                        <option value="">Vyberte typ importu</option>
                        <option value="categories">Kategorie</option>
                        <option value="products">Produkty</option>
                    </select>
                </div>
                
                <div id="formatInfo" class="format-info" style="display: none;">
                    <!-- Bude naplněno JavaScriptem -->
                </div>
                
                <div class="quick-add">
                    <h4>Rychlé přidání ukázkových dat</h4>
                    <div class="quick-buttons">
                        <button type="button" class="quick-btn" onclick="addSampleCategories()">Přidat ukázkové kategorie</button>
                        <button type="button" class="quick-btn" onclick="addSampleProducts()">Přidat ukázkové produkty</button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="csvData">CSV data (oddělené středníkem)</label>
                    <textarea id="csvData" name="csvData" placeholder="Vložte zde CSV data..."></textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">Importovat data</button>
            </form>
        </div>
    </div>

    <script>
    function updateFormatInfo() {
        const importType = document.getElementById('importType').value;
        const formatInfo = document.getElementById('formatInfo');
        
        if (importType === 'categories') {
            formatInfo.innerHTML = `
                <h4>Formát pro kategorie:</h4>
                <p><code>Název;Popis</code></p>
                <p><strong>Příklad:</strong></p>
                <code>Název;Popis<br>
                Metrické šrouby;Široká nabídka šroubů dle norem DIN/ISO<br>
                Matice;Standardní, pojistné a speciální matice</code>
            `;
            formatInfo.style.display = 'block';
        } else if (importType === 'products') {
            formatInfo.innerHTML = `
                <h4>Formát pro produkty:</h4>
                <p><code>Název;Popis;Cena;ID_Kategorie;Skladem</code></p>
                <p><strong>Příklad:</strong></p>
                <code>Název;Popis;Cena;ID_Kategorie;Skladem<br>
                Šroub M6x20;Nerezový šroub DIN 912;15.50;1;100<br>
                Matice M6;Nerezová matice DIN 934;8.50;2;200</code>
            `;
            formatInfo.style.display = 'block';
        } else {
            formatInfo.style.display = 'none';
        }
    }
    
    function addSampleCategories() {
        document.getElementById('importType').value = 'categories';
        updateFormatInfo();
        document.getElementById('csvData').value = `Název;Popis
Metrické šrouby;Široká nabídka šroubů dle norem DIN/ISO
Matice;Standardní, pojistné a speciální matice
Podložky;Ploché, pružné a další typy podložek
Vruty;Nerezové vruty do dřeva a kovu
Závitové tyče;Závitové tyče různých délek a průměrů`;
    }
    
    function addSampleProducts() {
        document.getElementById('importType').value = 'products';
        updateFormatInfo();
        document.getElementById('csvData').value = `Název;Popis;Cena;ID_Kategorie;Skladem
Šroub M6x20 DIN 912;Nerezový šroub s válcovou hlavou a vnitřním šestihranem;15.50;1;100
Šroub M8x25 DIN 912;Nerezový šroub s válcovou hlavou a vnitřním šestihranem;22.80;1;150
Matice M6 DIN 934;Nerezová šestihranná matice;8.50;2;200
Matice M8 DIN 934;Nerezová šestihranná matice;12.30;2;180
Podložka M6 DIN 125;Nerezová plochá podložka;3.20;3;300
Podložka M8 DIN 125;Nerezová plochá podložka;4.80;3;250
Vrut 4x40 DIN 7981;Nerezový samořezný vrut;5.60;4;120
Závitová tyč M8x1000;Nerezová závitová tyč;125.00;5;50`;
    }
    </script>
</body>
</html>

<%
categoriesRs.Close
Set categoriesRs = Nothing
%>
