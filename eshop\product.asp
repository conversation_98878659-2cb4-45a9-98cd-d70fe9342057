<%@ CodePage=65001 %>
<!--#include virtual="/includes/config.asp"-->
<!--#include virtual="/includes/database.asp"-->
<!--#include virtual="/includes/session.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

' Získání SEOUrl produktu z URL
Dim productUrl: productUrl = Request.QueryString("url")
Dim productId: productId = Null
Dim categoryId: categoryId = Null
Dim categoryName: categoryName = ""
Dim categorySEOUrl: categorySEOUrl = ""

' SEO optimalizace
Dim pageTitle, metaDesc
pageTitle = "Detail produktu - " & SITE_NAME
metaDesc = "Podrobné informace o produktu" ' Základní meta popis bez productRs

If productUrl = "" Then
    ' Pokud není zadána URL, přesměrujeme na katalog
    Response.Redirect "catalog.asp"
End If

' Načtení produktu z databáze podle SEOUrl
Dim productSql: productSql = "SELECT p.*, c.Name AS CategoryName, c.SEOUrl AS CategorySEOUrl " & _
                             "FROM Products p " & _
                             "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                             "WHERE p.SEOUrl = '" & Replace(productUrl, "'", "''") & "' " & _
                             "AND p.IsActive = 1"

Dim productRs: Set productRs = GetRecordset(productSql)

If productRs.EOF Then
    ' Produkt nebyl nalezen, přesměrujeme na katalog
    Response.Redirect "catalog.asp"
Else
    ' Nastavení proměnných podle načteného produktu
    productId = productRs("ProductID")
    categoryId = productRs("CategoryID")
    categoryName = productRs("CategoryName")
    categorySEOUrl = productRs("CategorySEOUrl")
    
    ' Aktualizace SEO informací
    pageTitle = productRs("Name") & " - " & SITE_NAME
    
    ' Jednoduchý meta popis bez složité logiky
    metaDesc = "Podrobné informace o produktu " & productRs("Name")
End If
%>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <title><%=pageTitle%></title>
    <meta name="description" content="<%=metaDesc%>">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!--#include virtual="/includes/header.asp"-->

    <div class="container">
        <% If Not IsNull(productId) Then %>
            <!-- Drobečková navigace -->
            <div class="breadcrumb-navigation">
                <a href="catalog.asp" class="breadcrumb-item">Domů</a>
                
                <% If Not IsNull(categoryId) Then %>
                    <span class="breadcrumb-separator">›</span>
                    <a href="catalog.asp?category=<%=Server.URLEncode(categorySEOUrl)%>" class="breadcrumb-item">
                        <%=categoryName%>
                    </a>
                <% End If %>
                
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-item current"><%=productRs("Name")%></span>
            </div>

            <!-- Detail produktu -->
            <div class="product-detail">
                <div class="product-gallery">
                    <div class="main-image">
                        <% If Not IsNull(productRs("MainImage")) Then %>
                            <img src="<%=PRODUCT_IMAGES_PATH & productRs("MainImage")%>" alt="<%=productRs("Name")%>" id="main-product-image">
                        <% Else %>
                            <img src="images/no-image.jpg" alt="<%=productRs("Name")%>" id="main-product-image">
                        <% End If %>
                    </div>
                    
                    <div class="thumbnail-gallery">
                        <% 
                        ' Načtení dalších obrázků produktu
                        Dim imagesSql: imagesSql = "SELECT ImageID, ImagePath " & _
                                                  "FROM ProductImages " & _
                                                  "WHERE ProductID = " & productId & " " & _
                                                  "ORDER BY OrderIndex"
                        Dim imagesRs: Set imagesRs = GetRecordset(imagesSql)
                        
                        If Not IsNull(productRs("MainImage")) Then
                        %>
                            <div class="thumbnail active" data-image="<%=PRODUCT_IMAGES_PATH & productRs("MainImage")%>">
                                <img src="<%=PRODUCT_IMAGES_PATH & productRs("MainImage")%>" alt="<%=productRs("Name")%>">
                            </div>
                        <% End If %>
                        
                        <% 
                        While Not imagesRs.EOF 
                            ' Změna: používáme ImagePath místo FileName
                            If imagesRs("ImagePath") <> productRs("MainImage") Then
                        %>
                            <div class="thumbnail" data-image="<%=PRODUCT_IMAGES_PATH & imagesRs("ImagePath")%>">
                                <img src="<%=PRODUCT_IMAGES_PATH & imagesRs("ImagePath")%>" alt="<%=productRs("Name")%>">
                            </div>
                        <% 
                            End If
                            imagesRs.MoveNext
                        Wend
                        Set imagesRs = Nothing
                        %>
                    </div>
                </div>
                
                <div class="product-info">
                    <h1 class="product-title"><%=productRs("Name")%></h1>
                    
                    <div class="product-meta">
                        <span class="product-code">Kód produktu: <%=productRs("SKU")%></span>
                        <span class="product-category">Kategorie: <a href="catalog.asp?category=<%=Server.URLEncode(categorySEOUrl)%>"><%=categoryName%></a></span>
                    </div>
                    
                    <div class="product-price-section">
                        <% If Not IsNull(productRs("DiscountPrice")) And productRs("DiscountPrice") > 0 Then %>
                            <div class="price-wrapper">
                                <span class="original-price"><%=FormatCurrency(productRs("Price"))%></span>
                                <span class="discount-price"><%=FormatCurrency(productRs("DiscountPrice"))%></span>
                                
                                <% 
                                Dim discountPercent: discountPercent = 0
                                If productRs("Price") > 0 Then
                                    discountPercent = Int((productRs("Price") - productRs("DiscountPrice")) / productRs("Price") * 100)
                                End If
                                %>
                                
                                <% If discountPercent > 0 Then %>
                                    <span class="discount-badge">-<%=discountPercent%>%</span>
                                <% End If %>
                            </div>
                        <% Else %>
                            <div class="price-wrapper">
                                <span class="price"><%=FormatCurrency(productRs("Price"))%></span>
                            </div>
                        <% End If %>
                        
                        <div class="stock-info">
                            <% If productRs("Stock") > 0 Then %>
                                <span class="in-stock">Skladem (<%=productRs("Stock")%> ks)</span>
                            <% Else %>
                                <span class="out-of-stock">Není skladem</span>
                            <% End If %>
                        </div>
                    </div>
                    
                    <div class="product-actions">
                        <div class="quantity-controls">
                            <button class="quantity-btn minus" onclick="updateQuantity(-1)">-</button>
                            <input type="number" id="product-quantity" value="1" min="1" max="<%=productRs("Stock")%>">
                            <button class="quantity-btn plus" onclick="updateQuantity(1)">+</button>
                        </div>
                        
                        <% If productRs("Stock") > 0 Then %>
                            <button onclick="cart.addItem(<%=productId%>, document.getElementById('product-quantity').value)" class="btn add-to-cart-btn">
                                <i class="icon-cart"></i> Do košíku
                            </button>
                        <% Else %>
                            <button class="btn add-to-cart-btn disabled">
                                Není skladem
                            </button>
                        <% End If %>
                    </div>
                    
                    <div class="product-description">
                        <h2>Popis produktu</h2>
                        <div class="description-content">
                            <% If Not IsNull(productRs("Description")) Then %>
                                <%=productRs("Description")%>
                            <% Else %>
                                <p>Pro tento produkt není k dispozici žádný popis.</p>
                            <% End If %>
                        </div>
                    </div>
                    
                    <div class="product-parameters">
                        <h2>Specifikace</h2>
                        <table class="parameters-table">
                            <% 
                            ' Načtení parametrů produktu
                            Dim paramsSql: paramsSql = "SELECT Name, Value " & _
                                                      "FROM ProductParameters " & _
                                                      "WHERE ProductID = " & productId & " " & _
                                                      "ORDER BY ParameterID"
                            Dim paramsRs: Set paramsRs = GetRecordset(paramsSql)
                            
                            If Not paramsRs.EOF Then
                                While Not paramsRs.EOF
                            %>
                                <tr>
                                    <th><%=paramsRs("Name")%>:</th>
                                    <td>
                                        <%=paramsRs("Value")%>
                                    </td>
                                </tr>
                            <% 
                                paramsRs.MoveNext
                                Wend
                            Else
                            %>
                                <tr>
                                    <td colspan="2">Pro tento produkt nejsou k dispozici žádné parametry.</td>
                                </tr>
                            <% 
                            End If
                            Set paramsRs = Nothing
                            %>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Související produkty -->
            <div class="related-products">
                <h2>Související produkty</h2>
                <div class="product-grid">
                    <% 
                    ' Načtení souvisejících produktů ze stejné kategorie
                    Dim relatedSql: relatedSql = "SELECT TOP 4 p.ProductID, p.Name, p.Price, p.DiscountPrice, " & _
                                                "p.Stock, p.SEOUrl, p.MainImage, " & _
                                                "c.Name AS CategoryName, c.SEOUrl AS CategoryUrl " & _
                                                "FROM Products p " & _
                                                "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                                                "WHERE p.IsActive = 1 AND p.CategoryID = " & productRs("CategoryID") & " " & _
                                                "AND p.ProductID <> " & productId & " " & _
                                                "ORDER BY NEWID()"
                    Dim relatedRs: Set relatedRs = GetRecordset(relatedSql)
                    
                    While Not relatedRs.EOF
                    %>
                        <div class="product-card">
                            <% If Not IsNull(relatedRs("MainImage")) Then %>
                                <img src="<%=PRODUCT_IMAGES_PATH & relatedRs("MainImage")%>" alt="<%=relatedRs("Name")%>">
                            <% End If %>
                            <span class="category-tag"><%=relatedRs("CategoryName")%></span>
                            <h3><%=relatedRs("Name")%></h3>
                            <p class="price"><%=FormatCurrency(relatedRs("Price"))%></p>
                            <div class="product-actions">
                                <% If relatedRs("Stock") > 0 Then %>
                                    <button onclick="cart.addItem(<%=relatedRs("ProductID")%>, 1)" class="btn">Do košíku</button>
                                <% Else %>
                                    <span class="out-of-stock">Není skladem</span>
                                <% End If %>
                                <a href="product.asp?url=<%=Server.URLEncode(relatedRs("SEOUrl"))%>" class="btn btn-secondary">Detail</a>
                            </div>
                        </div>
                    <%
                        relatedRs.MoveNext
                    Wend
                    Set relatedRs = Nothing
                    %>
                </div>
            </div>
        <% End If %>
    </div>



    <style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    /* Detail produktu */
    .product-detail {
        display: flex;
        gap: 40px;
        margin-bottom: 50px;
        background: #fff;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 1px 5px rgba(0,0,0,0.1);
    }

    /* Galerie produktu */
    .product-gallery {
        flex: 1;
        max-width: 500px;
    }

    .main-image {
        width: 100%;
        height: 400px;
        border: 1px solid #eee;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        background: #f9f9f9;
    }

    .main-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .thumbnail-gallery {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .thumbnail {
        width: 80px;
        height: 80px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9f9f9;
    }

    .thumbnail.active {
        border-color: #0094E1;
    }

    .thumbnail img {
        max-width: 95%;
        max-height: 95%;
        object-fit: contain;
    }

    /* Informace o produktu */
    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .product-title {
        font-size: 28px;
        margin: 0;
        color: #333;
    }

    .product-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        color: #666;
        font-size: 14px;
    }

    .product-meta a {
        color: #0094E1;
        text-decoration: none;
    }

    .product-meta a:hover {
        text-decoration: underline;
    }

    .product-price-section {
        margin: 15px 0;
        padding: 15px 0;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .price-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .price {
        font-size: 28px;
        font-weight: bold;
        color: #333;
    }

    .original-price {
        font-size: 18px;
        color: #999;
        text-decoration: line-through;
    }

    .discount-price {
        font-size: 28px;
        font-weight: bold;
        color: #e53935;
    }

    .discount-badge {
        background: #e53935;
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: bold;
    }

    .stock-info {
        margin-top: 10px;
    }

    .in-stock {
        color: #4caf50;
        font-weight: bold;
    }

    .out-of-stock {
        color: #e53935;
        font-weight: bold;
    }

    .product-actions {
        display: flex;
        align-items: center;
        gap: 15px;
        margin: 20px 0;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }

    .quantity-btn {
        width: 40px;
        height: 40px;
        background: #f9f9f9;
        border: none;
        font-size: 18px;
        cursor: pointer;
    }

    .quantity-btn:hover {
        background: #eee;
    }

    #product-quantity {
        width: 60px;
        height: 40px;
        border: none;
        text-align: center;
        font-size: 16px;
    }

    .add-to-cart-btn {
        height: 40px;
        padding: 0 30px;
        background: #0094E1;
        color: white;
        font-weight: bold;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .add-to-cart-btn:hover {
        background: #0077be;
    }

    .add-to-cart-btn.disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .product-description, .product-parameters {
        margin-top: 20px;
    }

    .product-description h2, .product-parameters h2, .related-products h2 {
        font-size: 20px;
        margin-bottom: 15px;
        color: #333;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .description-content {
        line-height: 1.6;
        color: #555;
    }

    .parameters-table {
        width: 100%;
        border-collapse: collapse;
    }

    .parameters-table tr:nth-child(odd) {
        background: #f9f9f9;
    }

    .parameters-table th, .parameters-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .parameters-table th {
        width: 30%;
        color: #666;
        font-weight: 600;
    }

    /* Související produkty */
    .related-products {
        margin-top: 40px;
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .product-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        display: flex;
        flex-direction: column;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .product-card img {
        width: 100%;
        height: 200px;
        object-fit: contain;
        background: #f9f9f9;
        padding: 10px;
    }

    @media (max-width: 992px) {
        .product-detail {
            flex-direction: column;
        }

        .product-gallery {
            max-width: 100%;
        }
    }
    </style>

    <script>
    // Funkce pro přepínání mezi náhledy obrázků
    document.addEventListener('DOMContentLoaded', function() {
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImage = document.getElementById('main-product-image');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Odstranění třídy "active" ze všech náhledů
                thumbnails.forEach(t => t.classList.remove('active'));
                
                // Přidání třídy "active" aktuálnímu náhledu
                this.classList.add('active');
                
                // Změna hlavního obrázku
                mainImage.src = this.dataset.image;
            });
        });
    });

    // Funkce pro aktualizaci množství
    function updateQuantity(change) {
        const input = document.getElementById('product-quantity');
        let value = parseInt(input.value) + change;
        
        // Kontrola minimálního množství
        if (value < 1) value = 1;
        
        // Kontrola maximálního množství
        const maxStock = parseInt(input.getAttribute('max'));
        if (maxStock > 0 && value > maxStock) value = maxStock;
        
        input.value = value;
    }

    // Objekt pro práci s košíkem (bude implementováno jinde)
    const cart = {
        addItem: function(productId, quantity) {
            console.log(`Přidávám do košíku produkt ID: ${productId}, množství: ${quantity}`);
            alert(`Produkt byl přidán do košíku (${quantity} ks)`);
            // Zde bude implementována funkce pro přidání do košíku
        }
    };
    </script>
</body>
</html> 