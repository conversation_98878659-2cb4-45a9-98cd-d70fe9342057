<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<%
Function GetRecommendations(productId)
    Dim http, url, apiKey, data
    Set http = Server.CreateObject("MSXML2.ServerXMLHTTP")
    
    url = "https://api.openai.com/v1/completions"
    apiKey = OPENAI_API_KEY
    
    ' Získání dat o produktu
    Dim conn, rs, productInfo
    Set conn = DBConnect()
    Set rs = Server.CreateObject("ADODB.Recordset")
    
    rs.Open "SELECT Name, Description, CategoryName FROM Products p " & _
            "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
            "WHERE ProductID=" & SafeSQL(productId), conn
            
    If rs.EOF Then
        GetRecommendations = "{""error"": ""Product not found""}"
        Exit Function
    End If
            
    productInfo = rs("Name") & " - " & rs("Description") & " (Kategorie: " & rs("CategoryName") & ")"
    rs.Close
    
    ' Sestavení požadavku pro OpenAI
    data = "{""model"": """ & OPENAI_MODEL & """, " & _
           """prompt"": ""Suggest 5 similar products to: " & productInfo & ". " & _
           "Return response as JSON array with product names only."", " & _
           """max_tokens"": 150, " & _
           """temperature"": 0.7}"
    
    On Error Resume Next
    http.Open "POST", url, False
    http.setRequestHeader "Content-Type", "application/json"
    http.setRequestHeader "Authorization", "Bearer " & apiKey
    http.send data
    
    ' Zpracování odpovědi
    If http.status = 200 Then
        Dim response, suggestions, jsonResponse
        response = http.responseText
        
        ' Smazání starých doporučení
        ExecuteSQL "DELETE FROM ProductRecommendations WHERE SourceProductID = " & SafeSQL(productId)
        
        ' Najít podobné produkty v databázi a uložit doporučení
        Set rs = GetRecordset("SELECT ProductID, Name FROM Products WHERE IsActive = 1")
        Dim products: Set products = Server.CreateObject("Scripting.Dictionary")
        
        While Not rs.EOF
            products.Add LCase(rs("Name")), rs("ProductID")
            rs.MoveNext
        Wend
        rs.Close
        
        ' Zpracování odpovědi z OpenAI a hledání shod
        Dim matches: Set matches = Server.CreateObject("Scripting.Dictionary")
        
        For Each key In products.Keys
            If InStr(LCase(response), key) > 0 And products(key) <> productId Then
                matches.Add products(key), 1.0
                
                ' Uložení doporučení do databáze
                sql = "INSERT INTO ProductRecommendations (SourceProductID, RecommendedProductID, Score) " & _
                      "VALUES (" & SafeSQL(productId) & ", " & SafeSQL(products(key)) & ", 1.0)"
                ExecuteSQL sql
            End If
        Next
        
        ' Sestavení JSON odpovědi
        Dim recommendedProducts
        Set rs = GetRecordset("SELECT p.ProductID, p.Name, p.Price, p.SEOUrl, p.MainImage " & _
                             "FROM ProductRecommendations pr " & _
                             "JOIN Products p ON pr.RecommendedProductID = p.ProductID " & _
                             "WHERE pr.SourceProductID = " & SafeSQL(productId))
        
        recommendedProducts = "{"
        recommendedProducts = recommendedProducts & """success"": true, ""recommendations"": ["
        
        Dim first: first = True
        While Not rs.EOF
            If Not first Then recommendedProducts = recommendedProducts & ","
            recommendedProducts = recommendedProducts & "{"
            recommendedProducts = recommendedProducts & """id"":" & rs("ProductID") & ","
            recommendedProducts = recommendedProducts & """name"":""" & rs("Name") & ""","
            recommendedProducts = recommendedProducts & """price"":" & rs("Price") & ","
            recommendedProducts = recommendedProducts & """url"":""" & rs("SEOUrl") & ""","
            recommendedProducts = recommendedProducts & """image"":""" & rs("MainImage") & """"
            recommendedProducts = recommendedProducts & "}"
            first = False
            rs.MoveNext
        Wend
        
        recommendedProducts = recommendedProducts & "]}"
        GetRecommendations = recommendedProducts
    Else
        GetRecommendations = "{""error"": ""API Error: " & http.status & """}"
    End If
    
    On Error Goto 0
    CloseDB conn
End Function

' Zpracování AJAX požadavku
If Request.QueryString("productId") <> "" Then
    Response.ContentType = "application/json"
    Response.Write GetRecommendations(Request.QueryString("productId"))
    Response.End
End If
%>

