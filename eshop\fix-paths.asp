<%@ CodePage=65001 %>
<%
' SCRIPT PRO OPRAVU CEST V E-SHOPU
' Tento script opraví všechny cesty v ASP souborech pro správné fungování z podadresáře

Response.Write "<html><head><title>Oprava cest e-shopu</title></head><body>"
Response.Write "<h1>Oprava cest pro e-shop v podadresáři</h1>"

' Seznam souborů k opravě
Dim filesToFix
filesToFix = Array("register.asp", "product.asp", "detail.asp", "reset-password.asp", "logout.asp")

Dim i
For i = 0 To UBound(filesToFix)
    Dim fileName
    fileName = filesToFix(i)
    
    Response.Write "<h3>Opravuji soubor: " & fileName & "</h3>"
    
    ' Kontrola existence souboru
    Dim fso
    Set fso = Server.CreateObject("Scripting.FileSystemObject")
    
    If fso.FileExists(Server.MapPath(fileName)) Then
        Dim file, content
        Set file = fso.OpenTextFile(Server.MapPath(fileName), 1)
        content = file.ReadAll
        file.Close
        
        ' Opravy v obsahu
        Dim originalContent
        originalContent = content
        
        ' Oprava virtual includes na file includes
        content = Replace(content, "<!--#include virtual=""/includes/", "<!--#include file=""includes/")
        
        ' Přidání Google Fonts pokud chybí
        If InStr(content, "fonts.googleapis.com") = 0 Then
            Dim headPos
            headPos = InStr(content, "</head>")
            If headPos > 0 Then
                Dim fontsCode
                fontsCode = vbCrLf & "    <link rel=""preconnect"" href=""https://fonts.googleapis.com"">" & vbCrLf & _
                           "    <link rel=""preconnect"" href=""https://fonts.gstatic.com"" crossorigin>" & vbCrLf & _
                           "    <link href=""https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap"" rel=""stylesheet"">" & vbCrLf
                content = Left(content, headPos - 1) & fontsCode & Mid(content, headPos)
            End If
        End If
        
        ' Přidání viewport meta tagu pokud chybí
        If InStr(content, "viewport") = 0 Then
            Dim charsetPos
            charsetPos = InStr(content, "charset=")
            If charsetPos > 0 Then
                Dim lineEndPos
                lineEndPos = InStr(charsetPos, content, vbCrLf)
                If lineEndPos > 0 Then
                    content = Left(content, lineEndPos) & "    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">" & Mid(content, lineEndPos)
                End If
            End If
        End If
        
        ' Uložení opravené verze
        If content <> originalContent Then
            Set file = fso.CreateTextFile(Server.MapPath(fileName), True)
            file.Write content
            file.Close
            Response.Write "<p style='color: green;'>✓ Soubor " & fileName & " byl úspěšně opraven</p>"
        Else
            Response.Write "<p style='color: blue;'>- Soubor " & fileName & " nevyžaduje opravy</p>"
        End If
    Else
        Response.Write "<p style='color: orange;'>⚠ Soubor " & fileName & " neexistuje</p>"
    End If
    
    Response.Flush
Next

' Oprava account/dashboard.asp
Response.Write "<h3>Opravuji soubor: account/dashboard.asp</h3>"
If fso.FileExists(Server.MapPath("account/dashboard.asp")) Then
    Set file = fso.OpenTextFile(Server.MapPath("account/dashboard.asp"), 1)
    content = file.ReadAll
    file.Close
    
    originalContent = content
    
    ' Oprava includes - přidání ../ pro cestu z podadresáře
    content = Replace(content, "<!--#include virtual=""/includes/", "<!--#include file=""../includes/")
    content = Replace(content, "<!--#include file=""includes/", "<!--#include file=""../includes/")
    
    ' Oprava CSS cesty
    content = Replace(content, "href=""css/styles.css""", "href=""../css/styles.css""")
    
    ' Oprava JS cesty
    content = Replace(content, "src=""js/", "src=""../js/")
    
    If content <> originalContent Then
        Set file = fso.CreateTextFile(Server.MapPath("account/dashboard.asp"), True)
        file.Write content
        file.Close
        Response.Write "<p style='color: green;'>✓ Soubor account/dashboard.asp byl úspěšně opraven</p>"
    Else
        Response.Write "<p style='color: blue;'>- Soubor account/dashboard.asp nevyžaduje opravy</p>"
    End If
Else
    Response.Write "<p style='color: orange;'>⚠ Soubor account/dashboard.asp neexistuje</p>"
End If

' Oprava account/history.asp
Response.Write "<h3>Opravuji soubor: account/history.asp</h3>"
If fso.FileExists(Server.MapPath("account/history.asp")) Then
    Set file = fso.OpenTextFile(Server.MapPath("account/history.asp"), 1)
    content = file.ReadAll
    file.Close
    
    originalContent = content
    
    ' Oprava includes
    content = Replace(content, "<!--#include virtual=""/includes/", "<!--#include file=""../includes/")
    content = Replace(content, "<!--#include file=""includes/", "<!--#include file=""../includes/")
    
    ' Oprava CSS cesty
    content = Replace(content, "href=""css/styles.css""", "href=""../css/styles.css""")
    
    ' Oprava JS cesty
    content = Replace(content, "src=""js/", "src=""../js/")
    
    If content <> originalContent Then
        Set file = fso.CreateTextFile(Server.MapPath("account/history.asp"), True)
        file.Write content
        file.Close
        Response.Write "<p style='color: green;'>✓ Soubor account/history.asp byl úspěšně opraven</p>"
    Else
        Response.Write "<p style='color: blue;'>- Soubor account/history.asp nevyžaduje opravy</p>"
    End If
Else
    Response.Write "<p style='color: orange;'>⚠ Soubor account/history.asp neexistuje</p>"
End If

Set fso = Nothing

Response.Write "<h2 style='color: green;'>Oprava cest dokončena!</h2>"
Response.Write "<p><a href='default.asp'>Zpět na hlavní stránku e-shopu</a></p>"
Response.Write "</body></html>"
%>
