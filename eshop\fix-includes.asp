<%@ CodePage=65001 %>
<%
' SCRIPT PRO OPRAVU INCLUDE CEST
Response.Write "<html><head><title>Oprava include cest</title></head><body>"
Response.Write "<h1>Oprava include cest v e-shopu</h1>"

' Seznam souborů k opravě
Dim filesToFix
filesToFix = Array("register.asp", "product.asp", "detail.asp", "reset-password.asp", "logout.asp")

Dim fso
Set fso = Server.CreateObject("Scripting.FileSystemObject")

Dim i
For i = 0 To UBound(filesToFix)
    Dim fileName
    fileName = filesToFix(i)
    
    Response.Write "<h3>Kontroluji soubor: " & fileName & "</h3>"
    
    If fso.FileExists(Server.MapPath(fileName)) Then
        Dim file, content
        Set file = fso.OpenTextFile(Server.MapPath(fileName), 1)
        content = file.ReadAll
        file.Close
        
        Dim originalContent
        originalContent = content
        
        ' Oprava virtual includes na file includes
        content = Replace(content, "<!--#include virtual=""/includes/", "<!--#include file=""includes/")
        
        ' Uložení opravené verze
        If content <> originalContent Then
            Set file = fso.CreateTextFile(Server.MapPath(fileName), True)
            file.Write content
            file.Close
            Response.Write "<p style='color: green;'>✓ Soubor " & fileName & " byl úspěšně opraven</p>"
        Else
            Response.Write "<p style='color: blue;'>- Soubor " & fileName & " nevyžaduje opravy</p>"
        End If
    Else
        Response.Write "<p style='color: orange;'>⚠ Soubor " & fileName & " neexistuje</p>"
    End If
    
    Response.Flush
Next

Set fso = Nothing

Response.Write "<h2 style='color: green;'>Oprava include cest dokončena!</h2>"
Response.Write "<p><a href='default.asp'>Zpět na hlavní stránku e-shopu</a></p>"
Response.Write "<p><a href='admin/dashboard.asp'>Přejít do administrace</a></p>"
Response.Write "</body></html>"
%>
