<%@ CodePage=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim message, email

' Zpracován<PERSON> formuláře
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    email = Trim(Request.Form("email"))
    
    If email <> "" Then
        ' <PERSON><PERSON><PERSON><PERSON>, zda uživatel existuje
        Dim userExists
        userExists = GetSingleValue("SELECT COUNT(*) FROM Users WHERE Email = " & SafeSQL(email))
        
        If userExists > 0 Then
            ' Nastavení admin práv
            ExecuteSQL "UPDATE Users SET IsAdmin = 1 WHERE Email = " & SafeSQL(email)
            message = "Uživatel " & email & " byl úspěšně povýšen na administrátora."
        Else
            message = "Uživatel s e-mailem " & email & " nebyl nalezen."
        End If
    Else
        message = "E-mail je povinný."
    End If
End If

' Získ<PERSON><PERSON> všech uživatelů
Dim usersRs
Set usersRs = GetRecordset("SELECT UserID, Email, FirstName, LastName, IsAdmin, Created FROM Users ORDER BY Created DESC")
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Správa admin práv - DINCO</title>
    <style>
    body {
        font-family: Arial, sans-serif;
        background-color: #1A1A1A;
        color: #DDDDDD;
        padding: 20px;
    }
    
    .admin-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #2A2A2A;
        padding: 30px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    h1 {
        color: #FFFFFF;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .form-section {
        background-color: #333333;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #DDDDDD;
        font-weight: 500;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px;
        border: 2px solid #444444;
        border-radius: 5px;
        background-color: #2A2A2A;
        color: #FFFFFF;
        font-size: 16px;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #007BFF;
    }
    
    .btn {
        background-color: #007BFF;
        color: white;
        padding: 12px 25px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: background-color 0.3s ease;
    }
    
    .btn:hover {
        background-color: #0056b3;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        text-align: center;
    }
    
    .message.success {
        background-color: #28a745;
        color: white;
    }
    
    .message.error {
        background-color: #dc3545;
        color: white;
    }
    
    .users-table {
        width: 100%;
        background-color: #333333;
        border-radius: 5px;
        overflow: hidden;
    }
    
    .users-table th,
    .users-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #444;
        color: #DDDDDD;
    }
    
    .users-table th {
        background-color: #444444;
        color: #FFFFFF;
        font-weight: bold;
    }
    
    .admin-badge {
        background-color: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .user-badge {
        background-color: #6c757d;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
    }
    
    .action-links {
        text-align: center;
        margin-top: 30px;
    }
    
    .action-links a {
        color: #007BFF;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 600;
    }
    
    .action-links a:hover {
        text-decoration: underline;
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>Správa admin práv</h1>
        
        <% If message <> "" Then %>
            <div class="message <% If InStr(message, "úspěšně") > 0 Then %>success<% Else %>error<% End If %>">
                <%=message%>
            </div>
        <% End If %>
        
        <div class="form-section">
            <h3 style="color: #FFFFFF; margin-bottom: 20px;">Povýšit uživatele na administrátora</h3>
            
            <form method="post">
                <div class="form-group">
                    <label for="email">E-mail uživatele</label>
                    <input type="email" id="email" name="email" value="<%=Request.Form("email")%>" required>
                </div>
                
                <button type="submit" class="btn">Povýšit na administrátora</button>
            </form>
        </div>
        
        <h3 style="color: #FFFFFF; margin-bottom: 20px;">Seznam uživatelů</h3>
        
        <% If Not usersRs.EOF Then %>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>E-mail</th>
                        <th>Jméno</th>
                        <th>Role</th>
                        <th>Vytvořen</th>
                    </tr>
                </thead>
                <tbody>
                    <% While Not usersRs.EOF %>
                    <tr>
                        <td><%=usersRs("UserID")%></td>
                        <td><%=usersRs("Email")%></td>
                        <td><%=usersRs("FirstName")%> <%=usersRs("LastName")%></td>
                        <td>
                            <% If usersRs("IsAdmin") = 1 Or usersRs("IsAdmin") = "1" Then %>
                                <span class="admin-badge">ADMIN</span>
                            <% Else %>
                                <span class="user-badge">Uživatel</span>
                            <% End If %>
                        </td>
                        <td><%=FormatDateTime(usersRs("Created"), 2)%></td>
                    </tr>
                    <% usersRs.MoveNext %>
                    <% Wend %>
                </tbody>
            </table>
        <% Else %>
            <p style="text-align: center; color: #BBBBBB;">Žádní uživatelé v databázi.</p>
        <% End If %>
        
        <div class="action-links">
            <a href="test-admin.asp">Test admin práv</a>
            <a href="create-admin.asp">Vytvořit nového admina</a>
            <a href="login.asp">Přihlášení</a>
            <a href="admin/dashboard.asp">Admin dashboard</a>
        </div>
    </div>
</body>
</html>

<%
usersRs.Close
Set usersRs = Nothing
%>
