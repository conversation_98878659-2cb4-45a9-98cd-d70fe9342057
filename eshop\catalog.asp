<%@ CodePage=65001 %>
<!--#include virtual="/includes/config.asp"-->
<!--#include virtual="/includes/database.asp"-->
<!--#include virtual="/includes/session.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

' SEO optimalizace
Dim pageTitle, metaDesc, catIdCount, allCategoriesSql
pageTitle = "Katalog produktů - " & SITE_NAME
metaDesc = "Kompletní nabídka našeho e-shopu. Vyberte si z široké nabídky kvalitních produktů."
%>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <title><%=pageTitle%></title>
    <meta name="description" content="<%=metaDesc%>">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!--#include virtual="/includes/header.asp"-->

    <div class="page-layout">
        <div class="categories-menu">
            <div class="menu-header">
                <i class="icon-menu"></i>
                <span>Všechny kategorie</span>
            </div>

            <ul class="main-categories">
                <%
                Dim mainCatRs, sql
                sql = "SELECT CategoryID, Name, SEOUrl, Icon " & _
                      "FROM Categories " & _
                      "WHERE ParentCategoryID IS NULL AND IsActive = 1 " & _
                      "ORDER BY OrderIndex, Name"
                Set mainCatRs = GetRecordset(sql)

                While Not mainCatRs.EOF
                %>
                    <li class="category-item" data-category="<%=mainCatRs("CategoryID")%>">
                        <a href="catalog.asp?category=<%=Server.URLEncode(mainCatRs("SEOUrl"))%>" class="category-link">
                            <% If Not IsNull(mainCatRs("Icon")) Then %>
                                <img src="<%=UPLOAD_PATH%>categories/<%=mainCatRs("Icon")%>" alt="" class="category-icon">
                            <% End If %>
                            <span class="category-name"><%=mainCatRs("Name")%></span>
                            <i class="icon-arrow"></i>
                        </a>

                        <div class="category-panel">
                            <div class="panel-content">
                                <div class="panel-header">
                                    <h2><%=mainCatRs("Name")%></h2>
                                </div>
                                <div class="subcategories-grid">
                                    <%
                                    Dim subCatRs, subSql
                                    subSql = "SELECT CategoryID, Name, SEOUrl, Icon " & _
                                            "FROM Categories " & _
                                            "WHERE ParentCategoryID = " & mainCatRs("CategoryID") & " " & _
                                            "AND IsActive = 1 " & _
                                            "ORDER BY OrderIndex, Name"
                                    Set subCatRs = GetRecordset(subSql)

                                    While Not subCatRs.EOF
                                    %>
                                        <div class="subcategory-column">
                                            <a href="catalog.asp?category=<%=Server.URLEncode(subCatRs("SEOUrl"))%>"
                                               class="subcategory-header">
                                                <% If Not IsNull(subCatRs("Icon")) Then %>
                                                    <img src="<%=UPLOAD_PATH%>categories/<%=subCatRs("Icon")%>" alt="" class="subcategory-icon">
                                                <% End If %>
                                                <span><%=subCatRs("Name")%></span>
                                            </a>

                                            <ul class="subcategory-list">
                                                <%
                                                Dim subSubCatRs, subSubSql
                                                subSubSql = "SELECT Name, SEOUrl " & _
                                                           "FROM Categories " & _
                                                           "WHERE ParentCategoryID = " & subCatRs("CategoryID") & " " & _
                                                           "AND IsActive = 1 " & _
                                                           "ORDER BY OrderIndex, Name"
                                                Set subSubCatRs = GetRecordset(subSubSql)

                                                While Not subSubCatRs.EOF
                                                %>
                                                    <li>
                                                        <a href="catalog.asp?category=<%=Server.URLEncode(subSubCatRs("SEOUrl"))%>">
                                                            <%=subSubCatRs("Name")%>
                                                        </a>
                                                    </li>
                                                <%
                                                    subSubCatRs.MoveNext
                                                Wend
                                                Set subSubCatRs = Nothing
                                                %>
                                            </ul>
                                        </div>
                                    <%
                                        subCatRs.MoveNext
                                    Wend
                                    Set subCatRs = Nothing
                                    %>
                                </div>
                            </div>
                        </div>
                    </li>
                <%
                    mainCatRs.MoveNext
                Wend
                Set mainCatRs = Nothing
                %>
            </ul>
        </div>

        <div class="catalog-content">

            <!-- Filtry a řazení -->
            <div class="catalog-filters">
                <div class="filter-group">
                    <label for="sort">Řadit podle:</label>
                    <select id="sort" name="sort">
                        <option value="name_asc">Název A-Z</option>
                        <option value="name_desc">Název Z-A</option>
                        <option value="price_asc">Nejlevnější</option>
                        <option value="price_desc">Nejdražší</option>
                        <option value="newest">Nejnovější</option>
                    </select>
                </div>
            </div>

            <%
            ' Získání parametrů z URL
            Dim sortBy: sortBy = Request.QueryString("sort")
            Dim page: page = Request.QueryString("page")
            If page = "" Then page = 1

            ' Získání ID kategorie podle SEOUrl
            Dim categoryId
            categoryId = Null

            If Request.QueryString("category") <> "" Then
                Dim catSql: catSql = "SELECT CategoryID, Name, SEOUrl FROM Categories " & _
                                     "WHERE LOWER(SEOUrl) = LOWER('" & Replace(Request.QueryString("category"), "'", "''") & "') " & _
                                     "AND IsActive = 1"

                Dim catRs: Set catRs = GetRecordset(catSql)
                If Not catRs.EOF Then
                    categoryId = catRs("CategoryID")
                End If
                Set catRs = Nothing
            End If

            ' Načtení podkategorií (přesunuto sem)
            Dim subcategoriesSql, subcategoriesRs
            If Not IsNull(categoryId) Then
                ' Nejdřív zobrazíme drobečkovou navigaci
                Dim breadcrumbSql, breadcrumbRs, breadcrumbPath
                breadcrumbPath = ""
                
                ' Načteme aktuální kategorii a všechny její nadřazené kategorie
                breadcrumbSql = "WITH CategoryHierarchy AS ( " & _
                                "  SELECT CategoryID, Name, SEOUrl, ParentCategoryID, 0 AS Level " & _
                                "  FROM Categories " & _
                                "  WHERE CategoryID = " & categoryId & " " & _
                                "  UNION ALL " & _
                                "  SELECT c.CategoryID, c.Name, c.SEOUrl, c.ParentCategoryID, ch.Level + 1 " & _
                                "  FROM Categories c " & _
                                "  INNER JOIN CategoryHierarchy ch ON c.CategoryID = ch.ParentCategoryID " & _
                                ") " & _
                                "SELECT CategoryID, Name, SEOUrl, ParentCategoryID, Level " & _
                                "FROM CategoryHierarchy " & _
                                "ORDER BY Level DESC"
                
                Set breadcrumbRs = GetRecordset(breadcrumbSql)
                
                If Not breadcrumbRs.EOF Then
                %>
                <div class="breadcrumb-navigation">
                    <a href="catalog.asp" class="breadcrumb-item">Domů</a>
                    <% 
                    While Not breadcrumbRs.EOF 
                        If breadcrumbRs("Level") > 0 Then
                    %>
                        <span class="breadcrumb-separator">›</span>
                        <a href="catalog.asp?category=<%=Server.URLEncode(breadcrumbRs("SEOUrl"))%>" class="breadcrumb-item">
                            <%=breadcrumbRs("Name")%>
                        </a>
                    <% 
                        Else
                    %>
                        <span class="breadcrumb-separator">›</span>
                        <span class="breadcrumb-item current"><%=breadcrumbRs("Name")%></span>
                    <%
                        End If
                        breadcrumbRs.MoveNext
                    Wend 
                    %>
                </div>
                <%
                End If
                Set breadcrumbRs = Nothing
                
                ' Pokračujeme s načtením podkategorií
                subcategoriesSql = "SELECT CategoryID, Name, SEOUrl FROM Categories WHERE ParentCategoryID = " & categoryId & " AND IsActive = 1 ORDER BY Name"

                Set subcategoriesRs = GetRecordset(subcategoriesSql)

                If Not subcategoriesRs.EOF Then
            %>
                <div class="subcategories-view">
                    <h2>Podkategorie</h2>
                    <div class="subcategories-list">
                        <% Do While Not subcategoriesRs.EOF %>
                            <div class="subcategory-item">
                                <a href="catalog.asp?category=<%=Server.URLEncode(subcategoriesRs("SEOUrl"))%>" class="subcategory-link">
                                    <span><%=subcategoriesRs("Name")%></span>
                                </a>
                            </div>
                        <% 
                            subcategoriesRs.MoveNext
                           Loop 
                        %>
                    </div>
                </div>
            <%
                End If
                subcategoriesRs.Close
                Set subcategoriesRs = Nothing
            End If
            %>

            <!-- Výpis produktů -->
            <div class="product-grid">
                <%
                If Not IsNull(categoryId) Then
                    allCategoriesSql = _
                        "SELECT CategoryID FROM Categories WHERE CategoryID = " & categoryId & " " & _
                        "UNION " & _
                        "SELECT CategoryID FROM Categories WHERE ParentCategoryID = " & categoryId & " " & _
                        "UNION " & _
                        "SELECT c2.CategoryID FROM Categories c1 " & _
                        "JOIN Categories c2 ON c2.ParentCategoryID = c1.CategoryID " & _
                        "WHERE c1.ParentCategoryID = " & categoryId

                    catIdCount = GetSingleValue("SELECT COUNT(*) FROM (" & allCategoriesSql & ") AS temp")
                Else
                    catIdCount = 0
                End If

                ' Sestavení SQL dotazu pro produkty
                Dim productSql: productSql = "SELECT TOP " & ITEMS_PER_PAGE & " p.ProductID, p.Name, p.Price, p.DiscountPrice, " & _
                                           "p.Stock, p.SEOUrl, p.MainImage, p.IsFeatured, " & _
                                           "c.Name AS CategoryName, c.SEOUrl AS CategoryUrl " & _
                                           "FROM Products p " & _
                                           "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                                           "WHERE p.IsActive = 1 "

                ' Přidání řazení
                Select Case sortBy
                    Case "name_asc"
                        productSql = productSql & "ORDER BY p.Name ASC"
                    Case "name_desc"
                        productSql = productSql & "ORDER BY p.Name DESC"
                    Case "price_asc"
                        productSql = productSql & "ORDER BY p.Price ASC"
                    Case "price_desc"
                        productSql = productSql & "ORDER BY p.Price DESC"
                    Case "newest"
                        productSql = productSql & "ORDER BY p.Created DESC"
                    Case Else
                        productSql = productSql & "ORDER BY p.Name ASC"
                End Select

                ' Přidání offsetu pro stránkování
                If CInt(page) > 1 Then
                    ' Pokud nejsme na první stránce, přidáme NOT IN klauzuli s produkty z předchozích stránek
                    Dim prevPageSql: prevPageSql = "SELECT TOP " & ((CInt(page) - 1) * ITEMS_PER_PAGE) & " ProductID FROM Products "
                    If Not IsNull(categoryId) Then
                        ' Nejprve definujeme allCategoriesSql
                        allCategoriesSql = _
                            "SELECT CategoryID FROM Categories WHERE CategoryID = " & categoryId & " " & _
                            "UNION " & _
                            "SELECT CategoryID FROM Categories WHERE ParentCategoryID = " & categoryId & " " & _
                            "UNION " & _
                            "SELECT c2.CategoryID FROM Categories c1 " & _
                            "JOIN Categories c2 ON c2.ParentCategoryID = c1.CategoryID " & _
                            "WHERE c1.ParentCategoryID = " & categoryId

                        ' Ověříme, že allCategoriesSql není prázdné
                        catIdCount = GetSingleValue("SELECT COUNT(*) FROM (" & allCategoriesSql & ") AS temp")

                        If catIdCount > 0 Then
                            ' Použijeme dočasnou tabulku pro kategorie, abychom se vyhnuli problémům se syntaxí
                            Dim tempCatSqlPaging
                            tempCatSqlPaging = "SELECT CategoryID FROM (" & allCategoriesSql & ") AS TempCat"

                            prevPageSql = "SELECT TOP " & ((CInt(page) - 1) * ITEMS_PER_PAGE) & " p.ProductID " & _
                                         "FROM Products p " & _
                                         "INNER JOIN (" & tempCatSqlPaging & ") AS ValidCats ON p.CategoryID = ValidCats.CategoryID " & _
                                         "WHERE p.IsActive = 1 "
                        Else
                            prevPageSql = prevPageSql & "WHERE IsActive = 1 "
                        End If
                    Else
                        prevPageSql = prevPageSql & "WHERE IsActive = 1 "
                    End If

                    ' Přidání řazení do subquery
                    Select Case sortBy
                        Case "name_asc"
                            prevPageSql = prevPageSql & "ORDER BY p.Name ASC"
                        Case "name_desc"
                            prevPageSql = prevPageSql & "ORDER BY p.Name DESC"
                        Case "price_asc"
                            prevPageSql = prevPageSql & "ORDER BY p.Price ASC"
                        Case "price_desc"
                            prevPageSql = prevPageSql & "ORDER BY p.Price DESC"
                        Case "newest"
                            prevPageSql = prevPageSql & "ORDER BY p.Created DESC"
                        Case Else
                            prevPageSql = prevPageSql & "ORDER BY p.Name ASC"
                    End Select

                    ' Pokud jsme vytvořili nový prevPageSql, použijeme ho přímo
                    If catIdCount > 0 Then
                        productSql = productSql & "AND p.ProductID NOT IN (" & prevPageSql & ") "
                    Else
                        ' Pokud nejsou kategorie, není třeba přidávat NOT IN klauzuli
                    End If
                End If

                ' Nejdřív definujeme countSql
                Dim countSql: countSql = "SELECT COUNT(*) AS Total " & _
                                         "FROM Products p " & _
                                         "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                                         "WHERE p.IsActive = 1 "

                ' Pak přidáme filtr podle kategorie do obou dotazů
                If Not IsNull(categoryId) Then
                    ' allCategoriesSql je již definováno výše

                    ' Úprava hlavních dotazů pro zahrnutí všech podkategorií
                    If catIdCount > 0 Then
                        ' Použijeme dočasnou tabulku pro kategorie, abychom se vyhnuli problémům se syntaxí
                        Dim tempCatSql
                        tempCatSql = "SELECT CategoryID FROM (" & allCategoriesSql & ") AS TempCat"

                        productSql = "SELECT p.ProductID, p.Name, p.Price, p.DiscountPrice, " & _
                                     "p.Stock, p.SEOUrl, p.MainImage, p.IsFeatured, " & _
                                     "c.Name AS CategoryName, c.SEOUrl AS CategoryUrl " & _
                                     "FROM Products p " & _
                                     "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                                     "INNER JOIN (" & tempCatSql & ") AS ValidCats ON p.CategoryID = ValidCats.CategoryID " & _
                                     "WHERE p.IsActive = 1 "
                    Else
                        ' Pokud nejsou nalezeny žádné kategorie, zobrazíme prázdný výsledek
                        productSql = "SELECT p.ProductID, p.Name, p.Price, p.DiscountPrice, " & _
                                     "p.Stock, p.SEOUrl, p.MainImage, p.IsFeatured, " & _
                                     "c.Name AS CategoryName, c.SEOUrl AS CategoryUrl " & _
                                     "FROM Products p " & _
                                     "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
                                     "WHERE 1=0 "  ' Vrátí prázdný výsledek
                    End If

                    ' Přidání řazení
                    Select Case sortBy
                        Case "name_asc"
                            productSql = productSql & "ORDER BY p.Name ASC"
                        Case "name_desc"
                            productSql = productSql & "ORDER BY p.Name DESC"
                        Case "price_asc"
                            productSql = productSql & "ORDER BY p.Price ASC"
                        Case "price_desc"
                            productSql = productSql & "ORDER BY p.Price DESC"
                        Case "newest"
                            productSql = productSql & "ORDER BY p.Created DESC"
                        Case Else
                            productSql = productSql & "ORDER BY p.Name ASC"
                    End Select

                    If catIdCount > 0 Then
                        countSql = "SELECT COUNT(*) AS Total " & _
                                   "FROM Products p " & _
                                   "INNER JOIN (" & tempCatSql & ") AS ValidCats ON p.CategoryID = ValidCats.CategoryID " & _
                                   "WHERE p.IsActive = 1"
                    Else
                        countSql = "SELECT 0 AS Total"  ' Vrátí nulu, pokud nejsou nalezeny žádné kategorie
                    End If
                End If

                ' Dokončení SQL dotazu - již nepotřebujeme další část, protože jsme použili TOP a NOT IN

                ' Načtení produktů
                Dim productRs: Set productRs = GetRecordset(productSql)

                While Not productRs.EOF
                %>
                    <div class="product-card">
                        <% If Not IsNull(productRs("MainImage")) Then %>
                            <img src="<%=PRODUCT_IMAGES_PATH & productRs("MainImage")%>" alt="<%=productRs("Name")%>">
                        <% End If %>
                        <span class="category-tag"><%=productRs("CategoryName")%></span>
                        <h3><%=productRs("Name")%></h3>
                        <p class="price"><%=FormatCurrency(productRs("Price"))%></p>
                        <div class="product-actions">
                            <% If productRs("Stock") > 0 Then %>
                                <button onclick="cart.addItem(<%=productRs("ProductID")%>, 1)" class="btn">Do košíku</button>
                            <% Else %>
                                <span class="out-of-stock">Není skladem</span>
                            <% End If %>
                            <a href="product.asp?url=<%=Server.URLEncode(productRs("SEOUrl"))%>" class="btn btn-secondary">Detail</a>
                        </div>
                    </div>
                <%
                    productRs.MoveNext
                Wend
                Set productRs = Nothing

                ' Získání celkového počtu produktů pro stránkování
                Dim totalProducts: totalProducts = GetSingleValue(countSql)
                Dim totalPages: totalPages = Int((totalProducts + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE)
                %>
            </div>

            <!-- Stránkování -->
            <% If totalPages > 1 Then %>
            <div class="pagination">
                <% If CInt(page) > 1 Then %>
                    <a href="?page=<%=CInt(page)-1%>&category=<%=Request.QueryString("category")%>&sort=<%=sortBy%>" class="btn">Předchozí</a>
                <% End If %>

                <% For i = 1 To totalPages %>
                    <%
                    Dim isActive
                    If CInt(page) = i Then
                        isActive = "active"
                    Else
                        isActive = ""
                    End If
                    %>
                    <a href="?page=<%=i%>&category=<%=Request.QueryString("category")%>&sort=<%=sortBy%>"
                       class="btn <%=isActive%>"><%=i%></a>
                <% Next %>

                <% If CInt(page) < totalPages Then %>
                    <a href="?page=<%=CInt(page)+1%>&category=<%=Request.QueryString("category")%>&sort=<%=sortBy%>" class="btn">Další</a>
                <% End If %>
            </div>
            <% End If %>
        </div>
    </div>
</div>

<style>
.page-layout {
    display: flex;
    gap: 20px;
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

/* Styly pro zobrazení podkategorií */
.subcategories-view {
    margin-bottom: 30px;
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Styly pro drobečkovou navigaci */
.breadcrumb-navigation {
    padding: 15px 20px;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    font-size: 14px;
}

.breadcrumb-item {
    color: #0094E1;
    text-decoration: none;
}

.breadcrumb-item:hover {
    text-decoration: underline;
}

.breadcrumb-item.current {
    color: #333;
    font-weight: bold;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: #999;
}

.subcategories-view h2 {
    font-size: 22px;
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.subcategories-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
}

.subcategory-item {
    background: #f8f8f8;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.subcategory-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.subcategory-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    text-decoration: none;
    color: #333;
    text-align: center;
    height: 100%;
}

.subcategory-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-bottom: 10px;
}

.categories-menu {
    width: 280px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    flex-shrink: 0;
}

.menu-header {
    padding: 15px;
    background: #0094E1;
    color: #fff;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: 4px 4px 0 0;
}

.main-categories {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    position: relative;
}

.category-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    text-decoration: none;
    color: #333;
    gap: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.category-link:hover {
    background: #f8f8f8;
}

.category-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.category-name {
    flex: 1;
}

.icon-arrow {
    color: #999;
}

.category-panel {
    display: none;
    position: absolute;
    left: 100%;
    top: 0;
    width: 900px;
    min-height: 400px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.category-item:hover .category-panel {
    display: block;
}

.panel-content {
    padding: 20px;
}

.panel-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.panel-header h2 {
    font-size: 24px;
    margin: 0;
    color: #333;
}

.subcategories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.subcategory-header {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: #333;
    font-weight: bold;
    margin-bottom: 10px;
}

.subcategory-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.subcategory-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.subcategory-list li a {
    display: block;
    padding: 5px 0;
    text-decoration: none;
    color: #666;
    font-size: 14px;
}

.subcategory-list li a:hover {
    color: #0094E1;
}

.catalog-content {
    flex: 1;
}

@media (max-width: 1200px) {
    .category-panel {
        width: 700px;
    }

    .subcategories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .page-layout {
        flex-direction: column;
    }

    .categories-menu {
        width: 100%;
    }

    .category-panel {
        position: static;
        width: 100%;
        display: none;
    }

    .category-item.active .category-panel {
        display: block;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 992) {
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if (e.target.closest('.category-link')) {
                    e.preventDefault();

                    const wasActive = this.classList.contains('active');

                    document.querySelectorAll('.category-item').forEach(cat => {
                        cat.classList.remove('active');
                    });

                    if (!wasActive) {
                        this.classList.add('active');
                    }
                }
            });
        });
    }
});
</script>
















































