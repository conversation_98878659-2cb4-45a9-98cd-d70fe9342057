<%@ CodePage=65001 %>
<%
' VYTVOŘENÍ UKÁZKOVÝCH DAT PRO DINCO E-SHOP

Dim newConnString
newConnString = "Provider=SQLOLEDB;Server=c109wq.forpsi.com;Database=f187884;User Id=f187884;Password=**************;"

Sub LogMessage(message)
    Response.Write "<p>" & Now() & ": " & message & "</p>" & vbCrLf
    Response.Flush
End Sub

Response.Write "<html><head><title>Vytvoření ukázkov<PERSON>ch dat</title></head><body>"
Response.Write "<h1>Vytvořen<PERSON> ukázkových dat pro DINCO E-shop</h1>"

On Error Resume Next

' Připojení k databázi
Dim conn
Set conn = Server.CreateObject("ADODB.Connection")
conn.Open newConnString

If Err.Number <> 0 Then
    LogMessage "CHYBA: Nelze se připojit k databázi: " & Err.Description
    Response.End
End If

LogMessage "✓ Připojení k databázi úspěšn<PERSON>"

' Vytvoření kategorií
LogMessage "Vytváření kategorií..."

Dim categories
categories = Array( _
    Array("Metrické šrouby", "Široká nabídka šroubů dle norem DIN/ISO", "metricke-srouby"), _
    Array("Matice", "Standardní, pojistné a speciální matice", "matice"), _
    Array("Podložky", "Ploché, pružné a další typy podložek", "podlozky"), _
    Array("Vruty", "Nerezové vruty do dřeva a kovu", "vruty"), _
    Array("Závitové tyče", "Závitové tyče různých délek a průměrů", "zavitove-tyče") _
)

Dim i
For i = 0 To UBound(categories)
    Dim catSQL
    catSQL = "INSERT INTO Categories (Name, Description, SEOUrl, IsActive, Created) VALUES (" & _
             "'" & categories(i)(0) & "'," & _
             "'" & categories(i)(1) & "'," & _
             "'" & categories(i)(2) & "'," & _
             "1," & _
             "GETDATE())"
    
    conn.Execute catSQL
    
    If Err.Number <> 0 Then
        LogMessage "Chyba při vytváření kategorie " & categories(i)(0) & ": " & Err.Description
        Err.Clear
    Else
        LogMessage "✓ Kategorie " & categories(i)(0) & " vytvořena"
    End If
Next

' Vytvoření produktů
LogMessage "Vytváření produktů..."

Dim products
products = Array( _
    Array("Šroub M6x20 DIN 912", "Nerezový šroub s válcovou hlavou a vnitřním šestihranem", 15.50, 1, "sroub-m6x20-din912"), _
    Array("Šroub M8x25 DIN 912", "Nerezový šroub s válcovou hlavou a vnitřním šestihranem", 22.80, 1, "sroub-m8x25-din912"), _
    Array("Šroub M10x30 DIN 912", "Nerezový šroub s válcovou hlavou a vnitřním šestihranem", 35.20, 1, "sroub-m10x30-din912"), _
    Array("Matice M6 DIN 934", "Nerezová šestihranná matice", 8.50, 2, "matice-m6-din934"), _
    Array("Matice M8 DIN 934", "Nerezová šestihranná matice", 12.30, 2, "matice-m8-din934"), _
    Array("Matice M10 DIN 934", "Nerezová šestihranná matice", 18.90, 2, "matice-m10-din934"), _
    Array("Podložka M6 DIN 125", "Nerezová plochá podložka", 3.20, 3, "podlozka-m6-din125"), _
    Array("Podložka M8 DIN 125", "Nerezová plochá podložka", 4.80, 3, "podlozka-m8-din125"), _
    Array("Podložka M10 DIN 125", "Nerezová plochá podložka", 7.50, 3, "podlozka-m10-din125"), _
    Array("Vrut 4x40 DIN 7981", "Nerezový samořezný vrut", 5.60, 4, "vrut-4x40-din7981"), _
    Array("Vrut 5x50 DIN 7981", "Nerezový samořezný vrut", 8.90, 4, "vrut-5x50-din7981"), _
    Array("Závitová tyč M8x1000", "Nerezová závitová tyč", 125.00, 5, "zavitova-tyc-m8x1000") _
)

For i = 0 To UBound(products)
    Dim prodSQL
    prodSQL = "INSERT INTO Products (CategoryID, Name, Description, Price, Stock, SEOUrl, IsActive, Created) VALUES (" & _
              products(i)(3) & "," & _
              "'" & products(i)(0) & "'," & _
              "'" & products(i)(1) & "'," & _
              products(i)(2) & "," & _
              "100," & _
              "'" & products(i)(4) & "'," & _
              "1," & _
              "GETDATE())"
    
    conn.Execute prodSQL
    
    If Err.Number <> 0 Then
        LogMessage "Chyba při vytváření produktu " & products(i)(0) & ": " & Err.Description
        Err.Clear
    Else
        LogMessage "✓ Produkt " & products(i)(0) & " vytvořen"
    End If
Next

' Vytvoření ukázkového uživatele
LogMessage "Vytváření ukázkového uživatele..."

Dim userSQL
userSQL = "INSERT INTO Users (Email, Password, FirstName, LastName, IsActive, Created) VALUES (" & _
          "'<EMAIL>'," & _
          "'$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'," & _
          "'Admin'," & _
          "'DINCO'," & _
          "1," & _
          "GETDATE())"

conn.Execute userSQL

If Err.Number <> 0 Then
    LogMessage "Chyba při vytváření uživatele: " & Err.Description
    Err.Clear
Else
    LogMessage "✓ Ukázkový uživatel vytvořen (<EMAIL> / heslo: password)"
End If

' Uzavření připojení
conn.Close
Set conn = Nothing

LogMessage "VYTVOŘENÍ UKÁZKOVÝCH DAT DOKONČENO!"
LogMessage "E-shop je nyní připraven k použití s ukázkovými daty."
LogMessage "<a href='default.asp'>Přejít na e-shop</a>"

Response.Write "</body></html>"
%>
