/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero sekce */
.hero-section {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
    text-align: center;
    margin-bottom: 50px;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3em;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 1.5em;
    margin-bottom: 30px;
}

/* Sekce */
section {
    margin-bottom: 60px;
}

section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
}

/* Produktové karty */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 15px;
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 15px;
}

.product-card h3 {
    font-size: 1.2em;
    margin-bottom: 10px;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4444;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
}

.price-container {
    margin: 15px 0;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    margin-right: 10px;
}

.discount-price {
    color: #ff4444;
    font-weight: bold;
}

.category-tag {
    background: #f0f0f0;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #666;
}

/* Kategorie */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.category-card {
    text-decoration: none;
    color: inherit;
    text-align: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: transform 0.2s;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.product-count {
    color: #666;
    font-size: 0.9em;
}

/* Výhody */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    text-align: center;
}

.benefit-card {
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
}

.benefit-card i {
    font-size: 2.5em;
    color: #4CAF50;
    margin-bottom: 15px;
}

/* Hlavička */
.main-header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 1.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-links a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
}

.nav-links a:hover {
    color: #007bff;
}

/* Formuláře */
.auth-form {
    max-width: 400px;
    margin: 2rem auto;
    padding: 2rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.error-message {
    color: #dc3545;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #dc3545;
    border-radius: 4px;
    background: #fff;
}

/* Dashboard */
.dashboard {
    padding: 2rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.dashboard-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.order-item, .cart-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.order-item:last-child, .cart-item:last-child {
    border-bottom: none;
}

/* Tlačítka */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background: #007bff;
    color: #fff;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn:hover {
    background: #0056b3;
}

.btn-link {
    background: none;
    color: #007bff;
}

.btn-link:hover {
    background: none;
    color: #0056b3;
    text-decoration: underline;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Stav skladu */
.out-of-stock {
    color: #ff4444;
    font-weight: bold;
}

/* Notifikace */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 15px 25px;
    border-radius: 4px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responzivní design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2em;
    }
    
    .hero-subtitle {
        font-size: 1.2em;
    }
    
    .product-grid,
    .category-grid,
    .benefits-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .product-grid,
    .category-grid,
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .product-actions {
        flex-direction: column;
    }
}

/* Kategorie menu */
.categories-menu {
    width: 280px;
    background: #fff;
    border: 1px solid #e0e0e0;
}

.main-categories {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    position: relative;
}

.category-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    text-decoration: none;
    color: #333;
}

.category-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.subcategories-panel {
    display: none;
    position: absolute;
    left: 100%;
    top: 0;
    width: 800px;
    background: #fff;
    border: 1px solid #e0e0e0;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
}

.category-item:hover .subcategories-panel {
    display: block;
}

.panel-content {
    padding: 20px;
}

.subcategories-columns {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.subcategory-column h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.subcategory-column a {
    color: #333;
    text-decoration: none;
}

.subcategory-column a:hover {
    color: #0066c0;
}

