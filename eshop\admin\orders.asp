<%@ CodePage=65001 %>
<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/security.asp"-->
<%
' Kontrola přihlášení administrátora
If Session("UserID") = "" Or Session("IsAdmin") <> "1" Then
    Response.Redirect "../login.asp?returnUrl=" & Server.URLEncode(Request.ServerVariables("SCRIPT_NAME"))
End If

Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim message

' Zpracování změny stavu objednávky
If Request.Form("action") = "updateStatus" Then
    Dim orderId, newStatus
    orderId = Request.Form("orderId")
    newStatus = Request.Form("status")
    
    ExecuteSQL "UPDATE Orders SET Status = " & SafeSQL(newStatus) & " WHERE OrderID = " & SafeSQL(orderId)
    message = "Stav objednávky byl úspěšně změněn."
End If

' Získání objednávek
Dim ordersRs, sql
sql = "SELECT o.OrderID, o.OrderNumber, o.TotalAmount, o.Status, o.Created, " & _
      "u.FirstName, u.LastName, u.Email " & _
      "FROM Orders o " & _
      "LEFT JOIN Users u ON o.UserID = u.UserID " & _
      "ORDER BY o.Created DESC"
Set ordersRs = GetRecordset(sql)
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Správa objednávek - DINCO Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        border: 2px solid #292d31;
    }
    
    .admin-nav {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .admin-nav a {
        background: #007BFF;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .admin-nav a:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .admin-nav a.active {
        background: #0056b3;
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .orders-table {
        width: 100%;
        background: #2A2A2A;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #292d31;
    }
    
    .orders-table th,
    .orders-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #444;
        color: #DDDDDD;
    }
    
    .orders-table th {
        background: #333;
        color: #FFFFFF;
        font-weight: bold;
    }
    
    .orders-table tr:hover {
        background: #333;
    }
    
    .status-pending {
        color: #ffc107;
        font-weight: bold;
    }
    
    .status-processing {
        color: #007bff;
        font-weight: bold;
    }
    
    .status-shipped {
        color: #17a2b8;
        font-weight: bold;
    }
    
    .status-delivered {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-cancelled {
        color: #dc3545;
        font-weight: bold;
    }
    
    .status-select {
        background: #333333;
        color: #FFFFFF;
        border: 1px solid #444;
        border-radius: 3px;
        padding: 5px;
        font-size: 12px;
    }
    
    .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }
    
    .action-buttons button,
    .action-buttons a {
        padding: 5px 10px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-view {
        background: #007BFF;
        color: white;
    }
    
    .btn-view:hover {
        background: #0056b3;
    }
    
    .btn-update {
        background: #28a745;
        color: white;
    }
    
    .btn-update:hover {
        background: #218838;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        background: #28a745;
        color: white;
        border: 1px solid #1e7e34;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        border: 2px solid #292d31;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007BFF;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #DDDDDD;
        font-size: 0.9rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px;
        color: #BBBBBB;
    }
    
    .empty-state h3 {
        color: #DDDDDD;
        margin-bottom: 10px;
    }
    
    @media (max-width: 768px) {
        .admin-nav {
            flex-direction: column;
        }
        
        .admin-nav a {
            text-align: center;
        }
        
        .orders-table {
            font-size: 14px;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>DINCO E-shop - Administrace</h1>
            <div class="admin-nav">
                <a href="dashboard.asp">Dashboard</a>
                <a href="products.asp">Produkty</a>
                <a href="categories.asp">Kategorie</a>
                <a href="orders.asp" class="active">Objednávky</a>
                <a href="import.asp">Import dat</a>
                <a href="../default.asp">Zpět na e-shop</a>
            </div>
        </div>

        <% If message <> "" Then %>
            <div class="message"><%=message%></div>
        <% End If %>

        <!-- Statistiky objednávek -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><%=GetSingleValue("SELECT COUNT(*) FROM Orders")%></div>
                <div class="stat-label">Celkem objednávek</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%=GetSingleValue("SELECT COUNT(*) FROM Orders WHERE Status = 'Pending'")%></div>
                <div class="stat-label">Čekající</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%=GetSingleValue("SELECT COUNT(*) FROM Orders WHERE Status = 'Processing'")%></div>
                <div class="stat-label">Zpracovávané</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%=GetSingleValue("SELECT COUNT(*) FROM Orders WHERE Status = 'Delivered'")%></div>
                <div class="stat-label">Doručené</div>
            </div>
        </div>

        <!-- Seznam objednávek -->
        <div class="admin-content">
            <h3>Seznam objednávek</h3>
            
            <% If Not ordersRs.EOF Then %>
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>Číslo objednávky</th>
                            <th>Zákazník</th>
                            <th>Celková částka</th>
                            <th>Stav</th>
                            <th>Datum</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% While Not ordersRs.EOF %>
                        <tr>
                            <td><strong><%=ordersRs("OrderNumber")%></strong></td>
                            <td>
                                <% If Not IsNull(ordersRs("FirstName")) Then %>
                                    <%=ordersRs("FirstName")%> <%=ordersRs("LastName")%><br>
                                    <small><%=ordersRs("Email")%></small>
                                <% Else %>
                                    <em>Neregistrovaný zákazník</em>
                                <% End If %>
                            </td>
                            <td><strong><%=FormatCurrency(ordersRs("TotalAmount"))%></strong></td>
                            <td>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="action" value="updateStatus">
                                    <input type="hidden" name="orderId" value="<%=ordersRs("OrderID")%>">
                                    <select name="status" class="status-select" onchange="this.form.submit()">
                                        <option value="Pending" <% If ordersRs("Status") = "Pending" Then %>selected<% End If %>>Čekající</option>
                                        <option value="Processing" <% If ordersRs("Status") = "Processing" Then %>selected<% End If %>>Zpracovává se</option>
                                        <option value="Shipped" <% If ordersRs("Status") = "Shipped" Then %>selected<% End If %>>Odesláno</option>
                                        <option value="Delivered" <% If ordersRs("Status") = "Delivered" Then %>selected<% End If %>>Doručeno</option>
                                        <option value="Cancelled" <% If ordersRs("Status") = "Cancelled" Then %>selected<% End If %>>Zrušeno</option>
                                    </select>
                                </form>
                            </td>
                            <td><%=FormatDateTime(ordersRs("Created"), 2)%></td>
                            <td>
                                <div class="action-buttons">
                                    <a href="order-detail.asp?id=<%=ordersRs("OrderID")%>" class="btn-view">Detail</a>
                                </div>
                            </td>
                        </tr>
                        <% ordersRs.MoveNext %>
                        <% Wend %>
                    </tbody>
                </table>
            <% Else %>
                <div class="empty-state">
                    <h3>Žádné objednávky</h3>
                    <p>V systému zatím nejsou žádné objednávky.</p>
                </div>
            <% End If %>
        </div>
    </div>
</body>
</html>

<%
ordersRs.Close
Set ordersRs = Nothing
%>
