<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/session.asp"-->
<%
CheckSession()

Dim userId: userId = Session("UserID")
Dim userInfo: Set userInfo = GetRecordset("SELECT * FROM Users WHERE UserID = " & SafeSQL(userId))
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mů<PERSON> ú<PERSON>et - <%=SITE_NAME%></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!--#include file="../includes/header.asp"-->
    
    <div class="container">
        <div class="dashboard">
            <h1>Můj účet</h1>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h2>Osobní údaje</h2>
                    <p><strong>Jméno:</strong> <%=userInfo("FirstName")%> <%=userInfo("LastName")%></p>
                    <p><strong>Email:</strong> <%=userInfo("Email")%></p>
                    <p><strong>Telefon:</strong> <%=userInfo("Phone")%></p>
                    <a href="edit-profile.asp" class="btn">Upravit údaje</a>
                </div>
                
                <div class="dashboard-card">
                    <h2>Moje objednávky</h2>
                    <% 
                    Dim orders: Set orders = GetRecordset("SELECT TOP 5 * FROM Orders WHERE UserID = " & SafeSQL(userId) & " ORDER BY Created DESC")
                    If Not orders.EOF Then
                        While Not orders.EOF
                    %>
                        <div class="order-item">
                            <p><strong>Objednávka č.:</strong> <%=orders("OrderID")%></p>
                            <p><strong>Datum:</strong> <%=FormatDateTime(orders("Created"), 2)%></p>
                            <p><strong>Stav:</strong> <%=orders("Status")%></p>
                            <a href="order-detail.asp?id=<%=orders("OrderID")%>">Detail objednávky</a>
                        </div>
                    <%
                        orders.MoveNext
                        Wend
                    Else
                    %>
                        <p>Zatím nemáte žádné objednávky.</p>
                    <% End If %>
                    <a href="orders.asp" class="btn">Všechny objednávky</a>
                </div>
                
                <div class="dashboard-card">
                    <h2>Uložené košíky</h2>
                    <% 
                    Dim savedCarts: Set savedCarts = GetRecordset("SELECT TOP 5 * FROM SavedCarts WHERE UserID = " & SafeSQL(userId) & " ORDER BY Created DESC")
                    If Not savedCarts.EOF Then
                        While Not savedCarts.EOF
                    %>
                        <div class="cart-item">
                            <p><strong>Název:</strong> <%=savedCarts("Name")%></p>
                            <p><strong>Vytvořeno:</strong> <%=FormatDateTime(savedCarts("Created"), 2)%></p>
                            <button onclick="cart.loadSavedCart(<%=savedCarts("CartID")%>)" class="btn">Načíst košík</button>
                        </div>
                    <%
                        savedCarts.MoveNext
                        Wend
                    Else
                    %>
                        <p>Nemáte žádné uložené košíky.</p>
                    <% End If %>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/cart.js"></script>
</body>
</html>

