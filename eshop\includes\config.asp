<%
' <PERSON><PERSON><PERSON><PERSON><PERSON> nastavení
Const SITE_NAME = "DINCO FASTENNERS E-shop"
Const SITE_URL = "https://www.dinco.cz"
Const ADMIN_EMAIL = "<EMAIL>"
Const ESHOP_BASE_PATH = "/eshop/"

' Nastavení stránkov<PERSON>í
Const ITEMS_PER_PAGE = 12
Const ADMIN_ITEMS_PER_PAGE = 20

' Cesty k souborům (upraveno pro podadresář)
Const UPLOAD_PATH = "/eshop/uploads/"
Const PRODUCT_IMAGES_PATH = "/eshop/uploads/products/"

' OpenAI API
Const OPENAI_API_KEY = "your-api-key"
Const OPENAI_MODEL = "text-davinci-003"

' Nastavení košíku
Const CART_TIMEOUT_MINUTES = 60

' Stavy objednávek
Const ORDER_STATUS_NEW = "NEW"
Const ORDER_STATUS_PAID = "PAID"
Const ORDER_STATUS_SHIPPED = "SHIPPED"
Const ORDER_STATUS_COMPLETED = "COMPLETED"
Const ORDER_STATUS_CANCELLED = "CANCELLED"

' Načtení dynamického nastavení z databáze
Function GetSetting(key)
    Dim sql
    sql = "SELECT Value FROM Settings WHERE [Key] = " & SafeSQL(key)
    GetSetting = GetSingleValue(sql)
End Function

' Cache pro nastavení
Dim Settings
Set Settings = Server.CreateObject("Scripting.Dictionary")

Function LoadSettings()
    Dim conn, rs
    Set conn = DBConnect()
    Set rs = conn.Execute("SELECT [Key], Value FROM Settings")
    
    While Not rs.EOF
        Settings(rs("Key")) = rs("Value")
        rs.MoveNext
    Wend
    
    rs.Close
    CloseDB conn
End Function

' Načtení nastavení při startu
LoadSettings()
%>
