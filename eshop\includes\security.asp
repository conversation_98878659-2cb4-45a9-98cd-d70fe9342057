<%
Function HashPassword(password)
    Dim stream
    Set stream = CreateObject("ADODB.Stream")
    
    Dim textToHash
    textToHash = password & "SaltForSecurity123" ' Salt by měl být unikátní pro každ<PERSON>ho uživatele
    
    stream.Type = 2 'Text
    stream.Charset = "UTF-8"
    stream.Open
    stream.WriteText textToHash
    
    Dim bytes
    stream.Position = 0
    stream.Type = 1 'Binary
    bytes = stream.Read
    
    stream.Close
    Set stream = Nothing
    
    HashPassword = BytesToHexString(bytes)
End Function

Function VerifyPassword(password, hashedPassword)
    VerifyPassword = (HashPassword(password) = hashedPassword)
End Function

Function StringToBytes(str)
    Dim i, bytes()
    ReDim bytes(Len(str) - 1)
    
    For i = 1 To Len(str)
        bytes(i-1) = AscB(Mid(str, i, 1))
    Next
    
    StringToBytes = bytes
End Function

Function BytesToHexString(bytes)
    Dim i, hexString
    hexString = ""
    
    For i = 1 To LenB(bytes)
        hexString = hexString & Right("0" & Hex(AscB(MidB(bytes, i, 1))), 2)
    Next
    
    BytesToHexString = hexString
End Function

Function ValidateEmail(email)
    Dim regEx
    Set regEx = New RegExp
    regEx.Pattern = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$"
    ValidateEmail = regEx.Test(email)
End Function
%>



