<%@ CodePage=65001 %>
<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/security.asp"-->
<%
' Kontrola přihlášení administrátora
If Session("UserID") = "" Or Session("IsAdmin") <> "1" Then
    Response.Redirect "../login.asp?returnUrl=" & Server.URLEncode(Request.ServerVariables("SCRIPT_NAME"))
End If

Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim productId, product, message, isEdit
productId = Request.QueryString("id")
isEdit = (productId <> "")

' Získání produktu pro editaci
If isEdit Then
    Set product = GetRecordset("SELECT * FROM Products WHERE ProductID = " & SafeSQL(productId))
    If product.EOF Then
        Response.Redirect "products.asp"
    End If
End If

' Zpracování formuláře
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim name, description, price, categoryId, stock, discountPrice, seoUrl
    
    name = Trim(Request.Form("name"))
    description = Trim(Request.Form("description"))
    price = Request.Form("price")
    categoryId = Request.Form("categoryId")
    stock = Request.Form("stock")
    discountPrice = Request.Form("discountPrice")
    seoUrl = CreateSEOUrl(name)
    
    ' Validace
    If name = "" Then
        message = "Název produktu je povinný."
    ElseIf Not IsNumeric(price) Or CDbl(price) < 0 Then
        message = "Cena musí být kladné číslo."
    ElseIf Not IsNumeric(stock) Or CInt(stock) < 0 Then
        message = "Skladové množství musí být nezáporné číslo."
    ElseIf categoryId = "" Or Not IsNumeric(categoryId) Then
        message = "Vyberte platnou kategorii."
    Else
        ' Uložení produktu
        Dim sql
        If isEdit Then
            sql = "UPDATE Products SET " & _
                  "Name = " & SafeSQL(name) & ", " & _
                  "Description = " & SafeSQL(description) & ", " & _
                  "Price = " & SafeSQL(price) & ", " & _
                  "CategoryID = " & SafeSQL(categoryId) & ", " & _
                  "Stock = " & SafeSQL(stock) & ", " & _
                  "DiscountPrice = " & IIf(discountPrice = "", "NULL", SafeSQL(discountPrice)) & ", " & _
                  "SEOUrl = " & SafeSQL(seoUrl) & " " & _
                  "WHERE ProductID = " & SafeSQL(productId)
        Else
            sql = "INSERT INTO Products (Name, Description, Price, CategoryID, Stock, DiscountPrice, SEOUrl, IsActive, Created) VALUES (" & _
                  SafeSQL(name) & ", " & SafeSQL(description) & ", " & SafeSQL(price) & ", " & _
                  SafeSQL(categoryId) & ", " & SafeSQL(stock) & ", " & _
                  IIf(discountPrice = "", "NULL", SafeSQL(discountPrice)) & ", " & _
                  SafeSQL(seoUrl) & ", 1, GETDATE())"
        End If
        
        ExecuteSQL sql
        Response.Redirect "products.asp"
    End If
End If

' Získání kategorií
Dim categoriesRs
Set categoriesRs = GetRecordset("SELECT CategoryID, Name FROM Categories WHERE IsActive = 1 ORDER BY Name")
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><% If isEdit Then %>Upravit produkt<% Else %>Přidat produkt<% End If %> - DINCO Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        border: 2px solid #292d31;
    }
    
    .admin-nav {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .admin-nav a {
        background: #007BFF;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .admin-nav a:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .form-container {
        background: #2A2A2A;
        padding: 30px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #DDDDDD;
        font-weight: 500;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #444444;
        border-radius: 5px;
        background-color: #333333;
        color: #FFFFFF;
        font-size: 16px;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #007BFF;
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 120px;
    }
    
    .required {
        color: #FF6B6B;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        background: #dc3545;
        color: white;
        border: 1px solid #c82333;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }
    
    .form-actions .btn {
        flex: 1;
        text-align: center;
        padding: 15px;
        font-size: 16px;
        font-weight: 600;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .admin-nav {
            flex-direction: column;
        }
        
        .admin-nav a {
            text-align: center;
        }
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>DINCO E-shop - Administrace</h1>
            <div class="admin-nav">
                <a href="dashboard.asp">Dashboard</a>
                <a href="products.asp">Produkty</a>
                <a href="categories.asp">Kategorie</a>
                <a href="orders.asp">Objednávky</a>
                <a href="import.asp">Import dat</a>
                <a href="../default.asp">Zpět na e-shop</a>
            </div>
        </div>

        <% If message <> "" Then %>
            <div class="message"><%=message%></div>
        <% End If %>

        <div class="form-container">
            <h2><% If isEdit Then %>Upravit produkt<% Else %>Přidat nový produkt<% End If %></h2>
            
            <form method="post">
                <div class="form-group">
                    <label for="name">Název produktu <span class="required">*</span></label>
                    <input type="text" id="name" name="name" value="<% If isEdit And Not product.EOF Then %><%=product("Name")%><% End If %>" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Popis produktu</label>
                    <textarea id="description" name="description" placeholder="Detailní popis produktu, specifikace, normy..."><% If isEdit And Not product.EOF Then %><%=product("Description")%><% End If %></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price">Cena (Kč) <span class="required">*</span></label>
                        <input type="number" step="0.01" id="price" name="price" value="<% If isEdit And Not product.EOF Then %><%=product("Price")%><% End If %>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="discountPrice">Akční cena (Kč)</label>
                        <input type="number" step="0.01" id="discountPrice" name="discountPrice" value="<% If isEdit And Not product.EOF And Not IsNull(product("DiscountPrice")) Then %><%=product("DiscountPrice")%><% End If %>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="categoryId">Kategorie <span class="required">*</span></label>
                        <select id="categoryId" name="categoryId" required>
                            <option value="">Vyberte kategorii</option>
                            <% While Not categoriesRs.EOF %>
                                <option value="<%=categoriesRs("CategoryID")%>" <% If isEdit And Not product.EOF And CStr(categoriesRs("CategoryID")) = CStr(product("CategoryID")) Then %>selected<% End If %>>
                                    <%=categoriesRs("Name")%>
                                </option>
                            <% categoriesRs.MoveNext %>
                            <% Wend %>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock">Skladem (ks) <span class="required">*</span></label>
                        <input type="number" id="stock" name="stock" value="<% If isEdit And Not product.EOF Then %><%=product("Stock")%><% Else %>0<% End If %>" required>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <% If isEdit Then %>Upravit produkt<% Else %>Přidat produkt<% End If %>
                    </button>
                    <a href="products.asp" class="btn btn-secondary">Zrušit</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>

<%
If isEdit And Not product Is Nothing Then
    product.Close
    Set product = Nothing
End If
categoriesRs.Close
Set categoriesRs = Nothing
%>
