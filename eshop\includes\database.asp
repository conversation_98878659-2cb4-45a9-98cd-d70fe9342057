<%
Function DBConnect()
    Dim conn
    Set conn = Server.CreateObject("ADODB.Connection")
    conn.Open "Provider=SQLOLEDB;Data Source=GPNEW\SQLEXPRESS;Initial Catalog=shop;Integrated Security=SSPI;"
    Set DBConnect = conn
End Function

Function CloseDB(conn)
    If Not conn Is Nothing Then
        conn.Close
        Set conn = Nothing
    End If
End Function

Function SafeSQL(value)
    If IsNull(value) Then
        SafeSQL = "NULL"
    ElseIf IsNumeric(value) Then
        SafeSQL = value
    Else
        SafeSQL = "'" & Replace(value, "'", "''") & "'"
    End If
End Function

Function GetRecordset(sql)
    Dim conn, rs
    On Error Resume Next
    Set conn = DBConnect()
    Set rs = Server.CreateObject("ADODB.Recordset")

    ' Pokus o otevření recordsetu
    rs.Open sql, conn

    ' Kontrola chyby
    If Err.Number <> 0 Then
        ' Zapíšeme chybu do logu
        Dim fso, logFile
        Set fso = Server.CreateObject("Scripting.FileSystemObject")
        Set logFile = fso.OpenTextFile(Server.MapPath("/sql_error.log"), 8, True)
        logFile.WriteLine("Datum a čas: " & Now())
        logFile.WriteLine("Chyba: " & Err.Description)
        logFile.WriteLine("SQL dotaz: " & sql)
        logFile.WriteLine("------------------------------------")
        logFile.Close
        Set logFile = Nothing
        Set fso = Nothing

        ' Vrátíme prázdný recordset
        Set rs = Server.CreateObject("ADODB.Recordset")
        rs.Fields.Append "Error", 200, 255
        rs.Open
        rs.AddNew
        rs("Error") = "Chyba v SQL dotazu: " & Err.Description
        rs.Update
        rs.MoveFirst
    End If
    On Error Goto 0

    Set GetRecordset = rs
    Set conn = Nothing
End Function

Function ExecuteSQL(sql)
    Dim conn
    On Error Resume Next
    Set conn = DBConnect()

    ' Pokus o provedení SQL dotazu
    conn.Execute sql

    ' Kontrola chyby
    If Err.Number <> 0 Then
        ' Zapíšeme chybu do logu
        Dim fso, logFile
        Set fso = Server.CreateObject("Scripting.FileSystemObject")
        Set logFile = fso.OpenTextFile(Server.MapPath("/sql_error.log"), 8, True)
        logFile.WriteLine("Datum a čas: " & Now())
        logFile.WriteLine("Chyba v ExecuteSQL: " & Err.Description)
        logFile.WriteLine("SQL dotaz: " & sql)
        logFile.WriteLine("------------------------------------")
        logFile.Close
        Set logFile = Nothing
        Set fso = Nothing
    End If
    On Error Goto 0

    CloseDB conn
End Function

Function GetSingleValue(sql)
    Dim rs
    On Error Resume Next
    Set rs = GetRecordset(sql)

    ' Kontrola, zda recordset obsahuje chybu
    If Not rs.EOF Then
        If rs.Fields.Count > 0 Then
            If rs.Fields(0).Name = "Error" Then
                ' Zapíšeme chybu do logu
                Dim fso, logFile
                Set fso = Server.CreateObject("Scripting.FileSystemObject")
                Set logFile = fso.OpenTextFile(Server.MapPath("/sql_error.log"), 8, True)
                logFile.WriteLine("Datum a čas: " & Now())
                logFile.WriteLine("Chyba v GetSingleValue: " & rs(0))
                logFile.WriteLine("SQL dotaz: " & sql)
                logFile.WriteLine("------------------------------------")
                logFile.Close
                Set logFile = Nothing
                Set fso = Nothing

                GetSingleValue = 0  ' Vrátíme 0 v případě chyby
            Else
                GetSingleValue = rs(0)
            End If
        Else
            GetSingleValue = Null
        End If
    Else
        GetSingleValue = Null
    End If

    If Not rs Is Nothing Then
        rs.Close
        Set rs = Nothing
    End If
    On Error Goto 0
End Function

Function GetProduct(productId)
    Dim sql, rs, product
    Set product = Server.CreateObject("Scripting.Dictionary")

    sql = "SELECT p.*, c.Name AS CategoryName " & _
          "FROM Products p " & _
          "LEFT JOIN Categories c ON p.CategoryID = c.CategoryID " & _
          "WHERE p.ProductID = " & SafeSQL(productId)

    Set rs = GetRecordset(sql)

    If Not rs.EOF Then
        For Each field in rs.Fields
            product.Add field.Name, field.Value
        Next
    End If

    rs.Close
    Set rs = Nothing
    Set GetProduct = product
End Function

Function GetProductByUrl(seoUrl)
    Dim sql
    sql = "SELECT ProductID FROM Products WHERE SEOUrl = " & SafeSQL(seoUrl)
    GetProductByUrl = GetSingleValue(sql)
End Function
%>
