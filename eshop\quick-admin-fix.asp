<%@ CodePage=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim action, message
action = Request.QueryString("action")

' Automatické nastavení admin práv pro aktuálně přihlášeného uživatele
If action = "makeCurrentUserAdmin" And Session("UserID") <> "" Then
    ExecuteSQL "UPDATE Users SET IsAdmin = 1 WHERE UserID = " & SafeSQL(Session("UserID"))
    Session("IsAdmin") = "1"
    message = "Aktuální uživatel byl povýšen na administrátora. Obnovte stránku."
End If

' Nastavení admin práv pro prvního uživatele v databázi
If action = "makeFirstUserAdmin" Then
    Dim firstUserId
    firstUserId = GetSingleValue("SELECT TOP 1 UserID FROM Users ORDER BY UserID")
    If firstUserId <> "" Then
        ExecuteSQL "UPDATE Users SET IsAdmin = 1 WHERE UserID = " & firstUserId
        message = "První uživatel v databázi byl povýšen na administrátora."
    Else
        message = "Žádní uživatelé v databázi."
    End If
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rychlá oprava admin práv - DINCO</title>
    <style>
    body {
        font-family: Arial, sans-serif;
        background-color: #1A1A1A;
        color: #DDDDDD;
        padding: 20px;
    }
    
    .fix-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #2A2A2A;
        padding: 30px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    h1 {
        color: #FFFFFF;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .status-section {
        background-color: #333333;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .fix-button {
        background-color: #007BFF;
        color: white;
        padding: 15px 25px;
        text-decoration: none;
        border-radius: 5px;
        display: inline-block;
        margin: 10px;
        font-weight: 600;
        transition: background-color 0.3s ease;
    }
    
    .fix-button:hover {
        background-color: #0056b3;
        text-decoration: none;
    }
    
    .fix-button.danger {
        background-color: #dc3545;
    }
    
    .fix-button.danger:hover {
        background-color: #c82333;
    }
    
    .fix-button.success {
        background-color: #28a745;
    }
    
    .fix-button.success:hover {
        background-color: #218838;
    }
    
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        text-align: center;
        background-color: #28a745;
        color: white;
    }
    
    .info-box {
        background-color: #17a2b8;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .warning-box {
        background-color: #ffc107;
        color: #212529;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .user-info {
        background-color: #444444;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
    }
    
    .action-links {
        text-align: center;
        margin-top: 30px;
    }
    
    .action-links a {
        color: #007BFF;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 600;
    }
    
    .action-links a:hover {
        text-decoration: underline;
    }
    </style>
</head>
<body>
    <div class="fix-container">
        <h1>🔧 Rychlá oprava admin práv</h1>
        
        <% If message <> "" Then %>
            <div class="message"><%=message%></div>
        <% End If %>
        
        <div class="info-box">
            <strong>Problém:</strong> Vidíte jen uživatelský účet místo admin dashboardu?<br>
            <strong>Řešení:</strong> Použijte tlačítka níže pro rychlé nastavení admin práv.
        </div>
        
        <!-- Aktuální stav -->
        <div class="status-section">
            <h3 style="color: #FFFFFF;">Aktuální stav:</h3>
            
            <% If Session("UserID") <> "" Then %>
                <%
                Dim currentUser
                Set currentUser = GetRecordset("SELECT * FROM Users WHERE UserID = " & SafeSQL(Session("UserID")))
                If Not currentUser.EOF Then
                %>
                    <div class="user-info">
                        <strong>Přihlášený uživatel:</strong><br>
                        ID: <%=currentUser("UserID")%><br>
                        E-mail: <%=currentUser("Email")%><br>
                        Jméno: <%=currentUser("FirstName")%> <%=currentUser("LastName")%><br>
                        Admin v DB: <%=IIf(currentUser("IsAdmin") = 1, "ANO", "NE")%><br>
                        Admin v Session: <%=IIf(Session("IsAdmin") = "1", "ANO", "NE")%>
                    </div>
                    
                    <% If currentUser("IsAdmin") <> 1 Then %>
                        <div class="warning-box">
                            ⚠️ Tento uživatel nemá admin práva v databázi.
                        </div>
                        <a href="?action=makeCurrentUserAdmin" class="fix-button success">
                            🚀 Povýšit mě na administrátora
                        </a>
                    <% Else %>
                        <div class="info-box">
                            ✅ Uživatel má admin práva v databázi.
                        </div>
                        
                        <% If Session("IsAdmin") <> "1" Then %>
                            <div class="warning-box">
                                ⚠️ Admin práva nejsou správně nastavena v session. Odhlaste se a přihlaste znovu.
                            </div>
                            <a href="logout.asp" class="fix-button">Odhlásit a přihlásit znovu</a>
                        <% Else %>
                            <div class="info-box">
                                ✅ Vše je v pořádku! Měli byste vidět admin dashboard.
                            </div>
                            <a href="admin/dashboard.asp" class="fix-button success">Přejít do administrace</a>
                        <% End If %>
                    <% End If %>
                <%
                    currentUser.Close
                    Set currentUser = Nothing
                Else
                %>
                    <div class="warning-box">
                        ⚠️ Uživatel nebyl nalezen v databázi.
                    </div>
                <% End If %>
            <% Else %>
                <div class="warning-box">
                    ⚠️ Nejste přihlášeni.
                </div>
                <a href="login.asp" class="fix-button">Přihlásit se</a>
            <% End If %>
        </div>
        
        <!-- Nouzové řešení -->
        <div class="status-section">
            <h3 style="color: #FFFFFF;">Nouzové řešení:</h3>
            <p>Pokud výše uvedené nefunguje, použijte tyto možnosti:</p>
            
            <a href="?action=makeFirstUserAdmin" class="fix-button danger">
                ⚡ Povýšit prvního uživatele na admina
            </a>
            
            <a href="create-admin.asp" class="fix-button">
                👤 Vytvořit nového admin účtu
            </a>
            
            <a href="make-admin.asp" class="fix-button">
                🔧 Správa admin práv
            </a>
        </div>
        
        <!-- Diagnostika -->
        <div class="status-section">
            <h3 style="color: #FFFFFF;">Diagnostika:</h3>
            <p>Počet admin uživatelů: <strong><%=GetSingleValue("SELECT COUNT(*) FROM Users WHERE IsAdmin = 1")%></strong></p>
            <p>Celkem uživatelů: <strong><%=GetSingleValue("SELECT COUNT(*) FROM Users")%></strong></p>
        </div>
        
        <div class="action-links">
            <a href="test-admin.asp">🔍 Detailní test</a>
            <a href="login.asp">🔑 Přihlášení</a>
            <a href="admin/dashboard.asp">⚙️ Admin dashboard</a>
            <a href="account/dashboard.asp">👤 Uživatelský účet</a>
        </div>
    </div>
</body>
</html>
