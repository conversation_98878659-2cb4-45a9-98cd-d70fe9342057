<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rych<PERSON>á Poptávka - DINCO FASTENNERS</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
        .quote-form-section {
            padding: 80px 0;
            background-color: #1A1A1A;
            min-height: calc(100vh - 200px);
        }

        .quote-form-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #2A2A2A;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .quote-form h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #FFFFFF;
            font-size: 2.5rem;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #DDDDDD;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #444444;
            border-radius: 5px;
            background-color: #333333;
            color: #FFFFFF;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007BFF;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .product-section {
            background-color: #333333;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 25px;
        }

        .product-section h3 {
            color: #FFFFFF;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            background-color: #2A2A2A;
            padding: 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .checkbox-group:hover {
            background-color: #404040;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
            transform: scale(1.2);
        }

        .checkbox-group label {
            margin: 0;
            cursor: pointer;
            color: #DDDDDD;
        }

        .submit-section {
            text-align: center;
            margin-top: 30px;
        }

        .submit-btn {
            background-color: #007BFF;
            color: #FFFFFF;
            padding: 15px 40px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background-color: #0056b3;
            box-shadow: 0px 0px 15px 3px #FFFBC4;
        }

        .required {
            color: #FF6B6B;
        }

        @media (max-width: 768px) {
            .quote-form-container {
                margin: 20px;
                padding: 25px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }

            .quote-form h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>

<body>
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="index.html"><img src="images/placeholder-logo.png" alt="DINCO Logo" style="height: 100px;"></a>
            </div>

            <!-- Hamburger menu button pro mobil -->
            <button class="menu-toggle" id="menuToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="index.html" onclick="closeMenu()">Domů</a></li>
                    <li><a href="#" onclick="closeMenu()">Produkty</a></li>
                    <li><a href="#" onclick="closeMenu()">Technické informace</a></li>
                    <li><a href="#" onclick="closeMenu()">O nás</a></li>
                    <li><a href="#" onclick="closeMenu()">Reference</a></li>
                    <li><a href="#" onclick="closeMenu()">Kontakty</a></li>
                </ul>
            </nav>
            <button class="cta-button header-cta" onclick="window.location.href='rychla-poptavka.html'">Rychlá Poptávka</button>
        </div>

        <!-- Overlay pro mobilní menu -->
        <div class="menu-overlay" id="menuOverlay" onclick="closeMenu()"></div>
    </header>

    <section class="quote-form-section">
        <div class="container">
            <div class="quote-form-container">
                <form class="quote-form" id="quoteForm" action="#" method="POST">
                    <h1>Rychlá Poptávka</h1>
                    
                    <!-- Kontaktní údaje -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">Jméno <span class="required">*</span></label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Příjmení <span class="required">*</span></label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">E-mail <span class="required">*</span></label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Telefon</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="company">Název firmy</label>
                        <input type="text" id="company" name="company">
                    </div>

                    <!-- Produkty -->
                    <div class="product-section">
                        <h3>Jaký typ produktů potřebujete?</h3>
                        <div class="product-grid">
                            <div class="checkbox-group">
                                <input type="checkbox" id="screws" name="products[]" value="screws">
                                <label for="screws">Metrické šrouby</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="nuts" name="products[]" value="nuts">
                                <label for="nuts">Matice</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="washers" name="products[]" value="washers">
                                <label for="washers">Podložky</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="bolts" name="products[]" value="bolts">
                                <label for="bolts">Vruty</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="threaded-rods" name="products[]" value="threaded-rods">
                                <label for="threaded-rods">Závitové tyče</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="other" name="products[]" value="other">
                                <label for="other">Ostatní</label>
                            </div>
                        </div>
                    </div>

                    <!-- Specifikace -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="material">Materiál</label>
                            <select id="material" name="material">
                                <option value="">Vyberte materiál</option>
                                <option value="A2">Nerez A2 (304)</option>
                                <option value="A4">Nerez A4 (316)</option>
                                <option value="other">Jiný materiál</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="quantity">Přibližné množství</label>
                            <input type="text" id="quantity" name="quantity" placeholder="např. 100 ks, 5 kg">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="specifications">Detailní specifikace</label>
                        <textarea id="specifications" name="specifications" placeholder="Uveďte prosím rozměry, normy (DIN/ISO), speciální požadavky atd."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="deadline">Požadovaný termín dodání</label>
                        <input type="date" id="deadline" name="deadline">
                    </div>

                    <div class="form-group">
                        <label for="message">Dodatečné informace</label>
                        <textarea id="message" name="message" placeholder="Jakékoliv další informace, které nám pomohou připravit přesnou nabídku"></textarea>
                    </div>

                    <div class="submit-section">
                        <button type="submit" class="submit-btn">Odeslat poptávku</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container footer-grid">
            <div class="footer-col">
                <h4>DINCO FASTENNERS</h4>
                <p>Váš spolehlivý dodavatel kvalitního nerezového spojovacího materiálu.</p>
            </div>
            <div class="footer-col">
                <h4>Rychlé odkazy</h4>
                <ul>
                    <li><a href="index.html">Domů</a></li>
                    <li><a href="#">Produkty</a></li>
                    <li><a href="#">Technické info</a></li>
                    <li><a href="#">O nás</a></li>
                    <li><a href="#">Kontakt</a></li>
                    <li><a href="#">Obchodní podmínky</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4>Kontakt</h4>
                <p>Ulice 123<br>Město, PSČ<br> Telefon: +420 123 456 789<br> Email: <EMAIL></p>
            </div>
            <div class="footer-col">
                <h4>Sledujte nás</h4>
                <p>IČO: 1<br>DIČ: CZ12345678</p>
            </div>
        </div>
        <div class="container footer-bottom">
            <p>&copy; 2025 DINCO FASTENNERS. Všechna práva vyhrazena.</p>
        </div>
    </footer>

    <script>
        // Hamburger menu functionality
        const menuToggle = document.getElementById('menuToggle');
        const mainNav = document.getElementById('mainNav');
        const menuOverlay = document.getElementById('menuOverlay');

        function toggleMenu() {
            menuToggle.classList.toggle('active');
            mainNav.classList.toggle('active');
            menuOverlay.classList.toggle('active');

            if (mainNav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMenu() {
            menuToggle.classList.remove('active');
            mainNav.classList.remove('active');
            menuOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        menuToggle.addEventListener('click', toggleMenu);

        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMenu();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMenu();
            }
        });

        // Form handling
        document.getElementById('quoteForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Zde by byla logika pro odeslání formuláře
            alert('Děkujeme za vaši poptávku! Ozveme se vám co nejdříve.');
            
            // Reset formuláře
            this.reset();
        });
    </script>

</body>

</html>
