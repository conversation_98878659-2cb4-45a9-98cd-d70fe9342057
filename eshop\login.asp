<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<!--#include file="includes/security.asp"-->
<%
Dim errorMessage
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim email, password, sql, rs
    email = Trim(Request.Form("email"))
    password = Trim(Request.Form("password"))
    
    If email <> "" And password <> "" Then
        sql = "SELECT UserID, ISNULL(IsAdmin, 0) AS IsAdmin, Password FROM Users WHERE Email = " & SafeSQL(email) & " AND IsActive = 1"
        Set rs = GetRecordset(sql)
        
        If Not rs.EOF Then
            If VerifyPassword(password, rs("Password").Value) Then
                ' Ukládáme přímo do Session jako ř<PERSON>zce
                Session("UserID") = CStr(rs("UserID").Value)
                Session("IsAdmin") = CStr(rs("IsAdmin").Value)
                Session.Timeout = 30
                
                ExecuteSQL "UPDATE Users SET LastLogin = GETDATE() WHERE UserID = " & rs("UserID").Value
                
                If Request.QueryString("returnUrl") <> "" Then
                    Response.Redirect Request.QueryString("returnUrl")
                Else
                    Response.Redirect "account/dashboard.asp"
                End If
            End If
        End If
        
        errorMessage = "Nesprávné přihlašovací údaje"
    Else
        errorMessage = "Vyplňte prosím email a heslo"
    End If
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Přihlášení - <%=SITE_NAME%></title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!--#include file="includes/header.asp"-->
    
    <div class="container">
        <div class="auth-form">
            <h1>Přihlášení</h1>
            
            <% If Request.QueryString("registered") = "1" Then %>
                <div class="success-message">Registrace proběhla úspěšně. Nyní se můžete přihlásit.</div>
            <% End If %>
            
            <% If Request.QueryString("passwordReset") = "1" Then %>
                <div class="success-message">Vaše heslo bylo úspěšně změněno. Nyní se můžete přihlásit s novým heslem.</div>
            <% End If %>
            
            <% If errorMessage <> "" Then %>
                <div class="error-message"><%=errorMessage%></div>
            <% End If %>
            
            <form method="post" action="login.asp<% If Request.QueryString("returnUrl") <> "" Then Response.Write "?returnUrl=" & Server.URLEncode(Request.QueryString("returnUrl")) End If %>">
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" value="<%=Request.Form("email")%>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Heslo:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Přihlásit se</button>
                    <a href="register.asp" class="btn btn-link">Registrace</a>
                </div>
                
                <div class="form-help">
                    <a href="reset-password.asp" class="forgot-password-link">Zapomenuté heslo?</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>














