<%@ CODEPAGE=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim cartItems, cartTotal
cartTotal = 0
Set cartItems = Server.CreateObject("Scripting.Dictionary")

If Request.Form("action") = "update" Then
    Response.ContentType = "application/json"
    Response.End
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ko<PERSON>ík - <%=SITE_NAME%></title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
<!--#include file="includes/header.asp"-->
    <div class="container">
        <h1>Nákupní ko<PERSON></h1>
        
        <div id="cart-content">
            <table class="cart-table">
                <thead>
                    <tr>
                        <th>Produkt</th>
                        <th>M<PERSON>žstv<PERSON></th>
                        <th>Cena/ks</th>
                        <th>Celkem</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody id="cart-items">
                    <!-- Položky košíku se načítají dynamicky přes JavaScript -->
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3">Celkem</td>
                        <td id="cart-total">0 Kč</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>

            <div class="cart-actions">
                <a href="catalog.asp" class="btn">Pokračovat v nákupu</a>
                <a href="checkout.asp" class="btn btn-primary">Pokračovat k pokladně</a>
            </div>
        </div>
    </div>

    <script>
    window.cartPrices = {
        <%
        sql = "SELECT ProductID, Price FROM Products WHERE IsActive = 1"
        Set rs = GetRecordset(sql)
        While Not rs.EOF
            Response.Write rs("ProductID") & ": " & rs("Price")
            If Not rs.EOF Then Response.Write ","
            rs.MoveNext
        Wend
        rs.Close
        %>
    };
    </script>
    <script src="js/cart.js"></script>
</body>
</html>
