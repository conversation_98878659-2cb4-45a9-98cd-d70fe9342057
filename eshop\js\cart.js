class Cart {
    constructor() {
        this.items = [];
        this.loadFromStorage();
        this.updateUI();
    }

    addItem(productId, quantity) {
        const existingItem = this.items.find(item => item.productId === productId);
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({ productId, quantity });
        }
        this.saveToStorage();
        this.updateUI();
        this.showNotification('Produkt byl přidán do k<PERSON>šíku');
    }

    removeItem(productId) {
        this.items = this.items.filter(item => item.productId !== productId);
        this.saveToStorage();
        this.updateUI();
    }

    updateQuantity(productId, quantity) {
        const item = this.items.find(item => item.productId === productId);
        if (item) {
            item.quantity = quantity;
            if (quantity <= 0) {
                this.removeItem(productId);
            }
        }
        this.saveToStorage();
        this.updateUI();
    }

    saveToStorage() {
        localStorage.setItem('cart', JSON.stringify(this.items));
    }

    loadFromStorage() {
        const stored = localStorage.getItem('cart');
        this.items = stored ? JSON.parse(stored) : [];
    }

    updateUI() {
        const cartCount = document.getElementById('cart-count');
        if (cartCount) {
            cartCount.textContent = this.items.reduce((sum, item) => sum + item.quantity, 0);
        }

        const cartTotal = document.getElementById('cart-total');
        if (cartTotal && window.cartPrices) {
            const total = this.items.reduce((sum, item) => {
                return sum + (window.cartPrices[item.productId] * item.quantity);
            }, 0);
            cartTotal.textContent = new Intl.NumberFormat('cs-CZ', { 
                style: 'currency', 
                currency: 'CZK' 
            }).format(total);
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    clear() {
        this.items = [];
        this.saveToStorage();
        this.updateUI();
    }

    saveCart(name) {
        const data = {
            name: name,
            items: this.items
        };

        return fetch('api/save-cart.asp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json());
    }

    loadSavedCart(cartId) {
        return fetch(`api/load-saved-cart.asp?cartId=${cartId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.items = data.items;
                    this.saveToStorage();
                    this.updateUI();
                }
                return data;
            });
    }
}

const cart = new Cart();

