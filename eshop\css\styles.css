/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    background-color: #1A1A1A;
    color: #DDDDDD;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Nadpisy v DINCO stylu */
h1,
h2,
h3,
h4 {
    font-family: 'Oswald', sans-serif;
    color: #FFFFFF;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    line-height: 1.2;
    text-shadow: 0px 0px 5px #000000;
}

h2 {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: #007BFF;
    margin: 0.5rem auto 0;
}

h3 {
    font-size: 1.5rem;
}

a {
    color: #00A1FF;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* <PERSON> sekce v DINCO stylu */
.hero-section {
    height: 55vh;
    min-height: 400px;
    position: relative;
    display: flex;
    align-items: center;
    background-position: top 0px left 0px;
    background-repeat: no-repeat;
    background-image: url('../../images/bckr.png');
    background-size: cover;
    margin-bottom: 50px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1;
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
    max-width: 62%;
    margin: 0 auto;
    color: #FFFFFF;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    text-shadow: 1px 3px 0 #969696a4, 1px 13px 5px #aba8a8c2;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #DDDDDD;
}

.hero-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-buttons .btn {
    margin: 0;
}

/* Sekce v DINCO stylu */
section {
    margin-bottom: 60px;
    padding: 60px 0;
}

.featured-section {
    background-color: #222222;
}

.categories-section {
    background-color: #1A1A1A;
}

.new-products-section {
    background-color: #222222;
}

.benefits-section {
    background-color: #1A1A1A;
}

/* Produktové karty v DINCO stylu */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background-color: #2A2A2A;
    border: 2px solid #292d31;
    border-radius: 8px;
    padding: 20px;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
    border-color: #d0e3f7;
    filter: brightness(1.1);
}

.product-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.product-card:hover img {
    transform: scale(1.05);
}

.product-card h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #FFFFFF;
}

.discount-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #007BFF;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.price-container {
    margin: 15px 0;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    margin-right: 10px;
    font-size: 1rem;
}

.discount-price {
    color: #007BFF;
    font-weight: bold;
    font-size: 1.2rem;
}

.price {
    color: #007BFF;
    font-weight: bold;
    font-size: 1.2rem;
}

.category-tag {
    background: #333333;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #DDDDDD;
    display: inline-block;
    margin-bottom: 8px;
}

.product-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.product-actions .btn {
    flex: 1;
}

/* Kategorie v DINCO stylu */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    background-color: #2A2A2A;
    padding: 30px;
    border-radius: 8px;
    border: 2px solid #292d31;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
    border-color: #d0e3f7;
    filter: brightness(1.2);
    text-decoration: none;
}

.category-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 15px;
}

.category-card h3 {
    color: #FFFFFF;
    margin-bottom: 8px;
}

.product-count {
    color: #BBBBBB;
    font-size: 0.9rem;
}

/* Výhody v DINCO stylu */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    text-align: center;
}

.benefit-card {
    background-color: #2A2A2A;
    padding: 30px;
    border-radius: 8px;
    border: 2px solid #292d31;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
}

.benefit-card i {
    font-size: 3rem;
    color: #00A1FF;
    margin-bottom: 1rem;
}

.benefit-card h3 {
    color: #FFFFFF;
    margin-bottom: 0.5rem;
}

.benefit-card p {
    color: #BBBBBB;
}

/* Hlavička v DINCO stylu */
.header {
    background-color: #1A1A1A;
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid #333;
    opacity: 0.95;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.logo img {
    height: 60px;
    transition: height 0.3s ease;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #FFFFFF;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    position: relative;
}

.main-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav ul li {
    margin-left: 25px;
    font-size: 1.2rem;
    font-weight: bold;
    text-shadow: 0px 5px 4px rgba(128, 103, 5, 0.89);
}

.main-nav ul li a {
    color: #FFFFFF;
    font-size: 1rem;
    padding-bottom: 5px;
    transition: color 0.3s ease, border-bottom-color 0.3s ease;
    border-bottom: 2px solid transparent;
    text-decoration: none;
}

.main-nav ul li a:hover {
    color: #00A1FF;
    border-bottom-color: #00A1FF;
    text-decoration: none;
}

.header-cta {
    background-color: #007BFF;
    color: #FFFFFF;
    margin-left: 20px;
    box-shadow: 5px 5px 15px 5px #000000;
}

.header-cta:hover {
    background-color: #0056b3;
    box-shadow: 0px 0px 9px 3px #FFFBC4;
}

/* Formuláře */
.auth-form {
    max-width: 400px;
    margin: 2rem auto;
    padding: 2rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.error-message {
    color: #dc3545;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #dc3545;
    border-radius: 4px;
    background: #fff;
}

/* Dashboard */
.dashboard {
    padding: 2rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.dashboard-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.order-item, .cart-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.order-item:last-child, .cart-item:last-child {
    border-bottom: none;
}

/* Tlačítka v DINCO stylu */
.btn {
    display: inline-block;
    padding: 10px 25px;
    border: none;
    border-radius: 5px;
    background: #007BFF;
    color: #FFFFFF;
    text-decoration: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
    text-align: center;
}

.btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0px 0px 9px 3px #FFFBC4;
    text-decoration: none;
}

.btn-primary {
    background-color: #007BFF;
}

.btn-secondary {
    background-color: transparent;
    color: #FFFFFF;
    border: 2px solid #FFFFFF;
}

.btn-secondary:hover {
    background-color: #FFFFFF;
    color: #1A1A1A;
    box-shadow: none;
}

.btn-link {
    background: none;
    color: #00A1FF;
    padding: 5px 10px;
}

.btn-link:hover {
    background: none;
    color: #0056b3;
    text-decoration: underline;
    transform: none;
    box-shadow: none;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Stav skladu */
.out-of-stock {
    color: #ff4444;
    font-weight: bold;
}

/* Notifikace */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 15px 25px;
    border-radius: 4px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responzivní design v DINCO stylu */
@media (max-width: 992px) {
    .hero-content {
        max-width: 80%;
    }
    .footer-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    /* Hamburger menu */
    .menu-toggle {
        display: block;
        flex-direction: column;
        width: 30px;
        height: 30px;
        justify-content: space-around;
        align-items: center;
    }

    .menu-toggle span {
        display: block;
        width: 25px;
        height: 3px;
        background-color: #FFFFFF;
        transition: all 0.3s ease;
        transform-origin: center;
    }

    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    .main-nav {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        height: 100vh;
        background-color: #1A1A1A;
        transition: right 0.3s ease;
        z-index: 1000;
        padding-top: 80px;
    }

    .main-nav.active {
        right: 0;
    }

    .main-nav ul {
        flex-direction: column;
        padding: 20px;
    }

    .main-nav ul li {
        margin: 0 0 20px 0;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .product-grid,
    .category-grid,
    .benefits-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .hero-section {
        height: auto;
        padding: 80px 0;
    }

    .hero-content {
        max-width: 100%;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .product-grid,
    .category-grid,
    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
        gap: 8px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .main-nav {
        width: 100%;
    }

    .container {
        padding: 0 15px;
    }
}

/* Kategorie menu */
.categories-menu {
    width: 280px;
    background: #fff;
    border: 1px solid #e0e0e0;
}

.main-categories {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    position: relative;
}

.category-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    text-decoration: none;
    color: #333;
}

.category-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.subcategories-panel {
    display: none;
    position: absolute;
    left: 100%;
    top: 0;
    width: 800px;
    background: #fff;
    border: 1px solid #e0e0e0;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
}

.category-item:hover .subcategories-panel {
    display: block;
}

.panel-content {
    padding: 20px;
}

.subcategories-columns {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.subcategory-column h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.subcategory-column a {
    color: #333;
    text-decoration: none;
}

.subcategory-column a:hover {
    color: #0066c0;
}

/* Footer v DINCO stylu */
.footer {
    background-color: #111111;
    padding: 40px 0 20px;
    color: #AAAAAA;
    font-size: 0.9rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-col h4 {
    color: #FFFFFF;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-col ul {
    list-style: none;
    padding: 0;
}

.footer-col ul li {
    margin-bottom: 8px;
}

.footer-col ul li a {
    color: #AAAAAA;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-col ul li a:hover {
    color: #00A1FF;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333;
}

/* Mobilní menu styly */
.menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 999;
}

.menu-overlay.active {
    display: block;
}

/* Out of stock styling */
.out-of-stock {
    color: #FF6B6B;
    font-weight: bold;
    padding: 8px 12px;
    background-color: #2A2A2A;
    border-radius: 4px;
    display: inline-block;
}

