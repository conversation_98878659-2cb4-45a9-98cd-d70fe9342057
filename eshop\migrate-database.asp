<%@ CodePage=65001 %>
<%
' DATABÁZOVÁ MIGRACE - POUŽIJTE OPATRNĚ!
' Tento script přenese data ze staré databáze do nové

' Konfigurace připojení
Dim oldConnString, newConnString
oldConnString = "Provider=SQLOLEDB;Data Source=GPNEW\SQLEXPRESS;Initial Catalog=shop;Integrated Security=SSPI;"
newConnString = "Provider=SQLOLEDB;Server=c109wq.forpsi.com;Database=f187884;User Id=f187884;Password=**************;"

' Bezpečnostní kontrola - odkomentujte až budete připraveni
' Response.Write "MIGRACE JE ZAKÁZÁNA PRO BEZPEČNOST"
' Response.End

Function ConnectToOldDB()
    Dim conn
    Set conn = Server.CreateObject("ADODB.Connection")
    conn.Open oldConnString
    Set ConnectToOldDB = conn
End Function

Function ConnectToNewDB()
    Dim conn
    Set conn = Server.CreateObject("ADODB.Connection")
    conn.Open newConnString
    Set ConnectToNewDB = conn
End Function

Sub LogMessage(message)
    Response.Write "<p>" & Now() & ": " & message & "</p>" & vbCrLf
    Response.Flush
End Sub

' Hlavní migrace
Response.Write "<html><head><title>Databázová migrace</title></head><body>"
Response.Write "<h1>Migrace databáze DINCO E-shop</h1>"

On Error Resume Next

' 1. Vytvoření tabulek v nové databázi
LogMessage "Vytváření struktury databáze..."

Dim newConn
Set newConn = ConnectToNewDB()

' SQL pro vytvoření tabulek
Dim createTablesSQL
createTablesSQL = "" & _
"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U') " & _
"CREATE TABLE Categories (" & _
"    CategoryID int IDENTITY(1,1) PRIMARY KEY," & _
"    Name nvarchar(100) NOT NULL," & _
"    Description ntext," & _
"    SEOUrl nvarchar(200)," & _
"    IsActive bit DEFAULT 1," & _
"    Created datetime DEFAULT GETDATE()" & _
");" & _
"" & _
"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U') " & _
"CREATE TABLE Products (" & _
"    ProductID int IDENTITY(1,1) PRIMARY KEY," & _
"    CategoryID int," & _
"    Name nvarchar(200) NOT NULL," & _
"    Description ntext," & _
"    Price decimal(10,2) NOT NULL," & _
"    DiscountPrice decimal(10,2)," & _
"    Stock int DEFAULT 0," & _
"    MainImage nvarchar(255)," & _
"    SEOUrl nvarchar(200)," & _
"    IsActive bit DEFAULT 1," & _
"    Created datetime DEFAULT GETDATE()," & _
"    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)" & _
");" & _
"" & _
"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U') " & _
"CREATE TABLE Users (" & _
"    UserID int IDENTITY(1,1) PRIMARY KEY," & _
"    Email nvarchar(100) UNIQUE NOT NULL," & _
"    Password nvarchar(255) NOT NULL," & _
"    FirstName nvarchar(50)," & _
"    LastName nvarchar(50)," & _
"    Phone nvarchar(20)," & _
"    Address ntext," & _
"    IsActive bit DEFAULT 1," & _
"    Created datetime DEFAULT GETDATE()" & _
");" & _
"" & _
"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Orders' AND xtype='U') " & _
"CREATE TABLE Orders (" & _
"    OrderID int IDENTITY(1,1) PRIMARY KEY," & _
"    UserID int," & _
"    OrderNumber nvarchar(50) UNIQUE," & _
"    TotalAmount decimal(10,2)," & _
"    Status nvarchar(50) DEFAULT 'Pending'," & _
"    ShippingAddress ntext," & _
"    Created datetime DEFAULT GETDATE()," & _
"    FOREIGN KEY (UserID) REFERENCES Users(UserID)" & _
");" & _
"" & _
"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderItems' AND xtype='U') " & _
"CREATE TABLE OrderItems (" & _
"    OrderItemID int IDENTITY(1,1) PRIMARY KEY," & _
"    OrderID int," & _
"    ProductID int," & _
"    Quantity int," & _
"    Price decimal(10,2)," & _
"    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)," & _
"    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)" & _
");"

newConn.Execute createTablesSQL

If Err.Number <> 0 Then
    LogMessage "CHYBA při vytváření tabulek: " & Err.Description
    Err.Clear
Else
    LogMessage "Struktura databáze vytvořena úspěšně"
End If

' 2. Migrace dat
LogMessage "Zahajování migrace dat..."

Dim oldConn, rs
Set oldConn = ConnectToOldDB()

' Migrace kategorií
LogMessage "Migrace kategorií..."
Set rs = Server.CreateObject("ADODB.Recordset")
rs.Open "SELECT * FROM Categories", oldConn

While Not rs.EOF
    Dim insertSQL
    insertSQL = "INSERT INTO Categories (Name, Description, SEOUrl, IsActive, Created) VALUES (" & _
                "'" & Replace(rs("Name"), "'", "''") & "'," & _
                "'" & Replace(rs("Description"), "'", "''") & "'," & _
                "'" & Replace(rs("SEOUrl"), "'", "''") & "'," & _
                rs("IsActive") & "," & _
                "'" & rs("Created") & "')"
    
    newConn.Execute insertSQL
    
    If Err.Number <> 0 Then
        LogMessage "Chyba při vkládání kategorie: " & Err.Description
        Err.Clear
    End If
    
    rs.MoveNext
Wend
rs.Close

LogMessage "Kategorie migrovány"

' Migrace produktů
LogMessage "Migrace produktů..."
rs.Open "SELECT * FROM Products", oldConn

While Not rs.EOF
    insertSQL = "INSERT INTO Products (CategoryID, Name, Description, Price, DiscountPrice, Stock, MainImage, SEOUrl, IsActive, Created) VALUES (" & _
                rs("CategoryID") & "," & _
                "'" & Replace(rs("Name"), "'", "''") & "'," & _
                "'" & Replace(rs("Description"), "'", "''") & "'," & _
                rs("Price") & "," & _
                IIf(IsNull(rs("DiscountPrice")), "NULL", rs("DiscountPrice")) & "," & _
                rs("Stock") & "," & _
                "'" & Replace(rs("MainImage"), "'", "''") & "'," & _
                "'" & Replace(rs("SEOUrl"), "'", "''") & "'," & _
                rs("IsActive") & "," & _
                "'" & rs("Created") & "')"
    
    newConn.Execute insertSQL
    
    If Err.Number <> 0 Then
        LogMessage "Chyba při vkládání produktu: " & Err.Description
        Err.Clear
    End If
    
    rs.MoveNext
Wend
rs.Close

LogMessage "Produkty migrovány"

' Migrace uživatelů (pokud existují)
On Error Resume Next
rs.Open "SELECT * FROM Users", oldConn
If Err.Number = 0 Then
    LogMessage "Migrace uživatelů..."
    While Not rs.EOF
        insertSQL = "INSERT INTO Users (Email, Password, FirstName, LastName, Phone, Address, IsActive, Created) VALUES (" & _
                    "'" & Replace(rs("Email"), "'", "''") & "'," & _
                    "'" & Replace(rs("Password"), "'", "''") & "'," & _
                    "'" & Replace(rs("FirstName"), "'", "''") & "'," & _
                    "'" & Replace(rs("LastName"), "'", "''") & "'," & _
                    "'" & Replace(rs("Phone"), "'", "''") & "'," & _
                    "'" & Replace(rs("Address"), "'", "''") & "'," & _
                    rs("IsActive") & "," & _
                    "'" & rs("Created") & "')"
        
        newConn.Execute insertSQL
        rs.MoveNext
    Wend
    rs.Close
    LogMessage "Uživatelé migrovány"
Else
    LogMessage "Tabulka Users neexistuje ve staré databázi"
    Err.Clear
End If

' Uzavření připojení
oldConn.Close
Set oldConn = Nothing
newConn.Close
Set newConn = Nothing

LogMessage "MIGRACE DOKONČENA!"
LogMessage "Zkontrolujte prosím data v nové databázi"

Response.Write "</body></html>"
%>
