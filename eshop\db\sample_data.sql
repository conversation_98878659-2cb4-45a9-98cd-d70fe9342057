-- Nastaven<PERSON> správn<PERSON><PERSON> kódování
SET LANGUAGE Czech;

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON> existuj<PERSON><PERSON>ch dat
DELETE FROM ProductParameters;
DELETE FROM ProductImages;
DELETE FROM OrderItems;
DELETE FROM Orders;
DELETE FROM SavedCartItems;
DELETE FROM SavedCarts;
DELETE FROM ProductRecommendations;
DELETE FROM Products;
DELETE FROM Categories;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> s explicitním N prefix pro Unicode stringy
INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'Obchodní zbož<PERSON>', N'Obchodní zboží a materiál', NULL, 'obchodni-zbozi', 1, 1);

DECLARE @ObchodniZboziID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'Nerezový spojovací materiál', N'Nerezový spojovací materiál různých typů', @ObchodniZboziID, 'nerezovy-spojovaci-material', 1, 1);

DECLARE @NerezovyMaterialID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'Šrouby', N'Různé typy šroubů', @NerezovyMaterialID, 'srouby', 1, 1);

DECLARE @SroubyID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'Šrouby do plechu', N'Šrouby určené do plechu', @SroubyID, 'srouby-do-plechu', 1, 1);

DECLARE @SroubyDoPlechuID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'Šrouby do plechu Al', N'Hliníkové šrouby do plechu', @SroubyDoPlechuID, 'srouby-do-plechu-al', 1, 1);

DECLARE @SroubyDoPlechuAlID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'DIN 7982', N'Šrouby dle normy DIN 7982', @SroubyDoPlechuAlID, 'din-7982', 1, 1);

DECLARE @DIN7982ID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES 
(N'DIN 7982 C', N'Šrouby dle normy DIN 7982 C', @DIN7982ID, 'din-7982-c', 1, 1);

DECLARE @DIN7982CID INT = SCOPE_IDENTITY();

-- Vložení produktů
INSERT INTO Products (
    SKU, 
    Name, 
    Description, 
    Price, 
    Stock, 
    CategoryID, 
    SEOUrl, 
    IsActive
)
VALUES 
(N'9J60000101', N'šroub 2.2 x 13 , ( záp.hl+kř. ) DIN7982C/A2', N'Nerezový šroub do plechu s křížovou hlavou', 1.50, 100, @DIN7982CID, 'sroub-2-2x13-din7982c-a2', 1),
(N'**********', N'šroub 2.2 x 16 , ( záp.hl+kř. ) DIN7982C/A2', N'Nerezový šroub do plechu s křížovou hlavou', 1.70, 100, @DIN7982CID, 'sroub-2-2x16-din7982c-a2', 1),
(N'6480000101', N'šroub 2.2 x 22 , ( záp.hl+kř. ) DIN7982C/A2', N'Nerezový šroub do plechu s křížovou hlavou', 1.90, 100, @DIN7982CID, 'sroub-2-2x22-din7982c-a2', 1),
(N'**********', N'šroub 2.9 x 6.5 , ( záp.hl+kř. ) DIN7982C/A2', N'Nerezový šroub do plechu s křížovou hlavou', 1.30, 100, @DIN7982CID, 'sroub-2-9x6-5-din7982c-a2', 1),
(N'9020000101', N'šroub 2.9 x 9.5 , ( záp.hl+kř. ) DIN7982C/A2', N'Nerezový šroub do plechu s křížovou hlavou', 1.40, 100, @DIN7982CID, 'sroub-2-9x9-5-din7982c-a2', 1);

-- Vložení parametrů produktů
INSERT INTO ProductParameters (ProductID, Name, Value)
SELECT 
    ProductID,
    N'CODE',
    CASE 
        WHEN SKU = '9J60000101' THEN N'107982C0022013'
        WHEN SKU = '**********' THEN N'107982C0022016'
        WHEN SKU = '6480000101' THEN N'107982C0022022'
        WHEN SKU = '**********' THEN N'107982C0029006'
        WHEN SKU = '9020000101' THEN N'107982C0029009'
    END
FROM Products
WHERE SKU IN ('9J60000101', '**********', '6480000101', '**********', '9020000101');

