<%@ CodePage=65001 %>
<!--#include file="../includes/config.asp"-->
<!--#include file="../includes/database.asp"-->
<!--#include file="../includes/security.asp"-->
<%
' Kontrola přihlášení administrátora
If Session("UserID") = "" Or Session("IsAdmin") <> "1" Then
    Response.Redirect "../login.asp?returnUrl=" & Server.URLEncode(Request.ServerVariables("SCRIPT_NAME"))
End If

Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim exportType
exportType = Request.QueryString("type")

' Zpracování exportu
If exportType <> "" Then
    Select Case exportType
        Case "categories"
            Response.ContentType = "text/csv; charset=utf-8"
            Response.AddHeader "Content-Disposition", "attachment; filename=kategorie_" & Replace(Date(), "/", "-") & ".csv"
            
            Dim categoriesRs
            Set categoriesRs = GetRecordset("SELECT Name, Description, SEOUrl, IsActive FROM Categories ORDER BY Name")
            
            Response.Write "Název;Popis;SEO URL;Aktivní" & vbCrLf
            
            While Not categoriesRs.EOF
                Response.Write """" & Replace(categoriesRs("Name"), """", """""") & """;"
                Response.Write """" & Replace(categoriesRs("Description"), """", """""") & """;"
                Response.Write """" & categoriesRs("SEOUrl") & """;"
                Response.Write IIf(categoriesRs("IsActive"), "Ano", "Ne") & vbCrLf
                categoriesRs.MoveNext
            Wend
            
            categoriesRs.Close
            Set categoriesRs = Nothing
            Response.End
            
        Case "products"
            Response.ContentType = "text/csv; charset=utf-8"
            Response.AddHeader "Content-Disposition", "attachment; filename=produkty_" & Replace(Date(), "/", "-") & ".csv"
            
            Dim productsRs
            Set productsRs = GetRecordset("SELECT p.Name, p.Description, p.Price, p.DiscountPrice, p.Stock, p.SEOUrl, p.IsActive, c.Name AS CategoryName FROM Products p LEFT JOIN Categories c ON p.CategoryID = c.CategoryID ORDER BY p.Name")
            
            Response.Write "Název;Popis;Cena;Akční cena;Skladem;SEO URL;Kategorie;Aktivní" & vbCrLf
            
            While Not productsRs.EOF
                Response.Write """" & Replace(productsRs("Name"), """", """""") & """;"
                Response.Write """" & Replace(productsRs("Description"), """", """""") & """;"
                Response.Write productsRs("Price") & ";"
                Response.Write IIf(IsNull(productsRs("DiscountPrice")), "", productsRs("DiscountPrice")) & ";"
                Response.Write productsRs("Stock") & ";"
                Response.Write """" & productsRs("SEOUrl") & """;"
                Response.Write """" & productsRs("CategoryName") & """;"
                Response.Write IIf(productsRs("IsActive"), "Ano", "Ne") & vbCrLf
                productsRs.MoveNext
            Wend
            
            productsRs.Close
            Set productsRs = Nothing
            Response.End
            
        Case "orders"
            Response.ContentType = "text/csv; charset=utf-8"
            Response.AddHeader "Content-Disposition", "attachment; filename=objednavky_" & Replace(Date(), "/", "-") & ".csv"
            
            Dim ordersRs
            Set ordersRs = GetRecordset("SELECT o.OrderNumber, o.TotalAmount, o.Status, o.Created, u.FirstName, u.LastName, u.Email FROM Orders o LEFT JOIN Users u ON o.UserID = u.UserID ORDER BY o.Created DESC")
            
            Response.Write "Číslo objednávky;Celková částka;Stav;Datum;Jméno;Příjmení;E-mail" & vbCrLf
            
            While Not ordersRs.EOF
                Response.Write """" & ordersRs("OrderNumber") & """;"
                Response.Write ordersRs("TotalAmount") & ";"
                Response.Write """" & ordersRs("Status") & """;"
                Response.Write """" & FormatDateTime(ordersRs("Created"), 2) & """;"
                Response.Write """" & IIf(IsNull(ordersRs("FirstName")), "", ordersRs("FirstName")) & """;"
                Response.Write """" & IIf(IsNull(ordersRs("LastName")), "", ordersRs("LastName")) & """;"
                Response.Write """" & IIf(IsNull(ordersRs("Email")), "", ordersRs("Email")) & """" & vbCrLf
                ordersRs.MoveNext
            Wend
            
            ordersRs.Close
            Set ordersRs = Nothing
            Response.End
    End Select
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Export dat - DINCO Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Oswald:wght@400;500&display=swap" rel="stylesheet">
    <style>
    .admin-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: #2A2A2A;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        border: 2px solid #292d31;
    }
    
    .admin-nav {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .admin-nav a {
        background: #007BFF;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .admin-nav a:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
    }
    
    .export-section {
        background: #2A2A2A;
        padding: 25px;
        border-radius: 8px;
        border: 2px solid #292d31;
        margin-bottom: 30px;
    }
    
    .export-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .export-card {
        background: #333333;
        padding: 20px;
        border-radius: 8px;
        border: 2px solid #444444;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .export-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
        border-color: #d0e3f7;
    }
    
    .export-card h3 {
        color: #FFFFFF;
        margin-bottom: 15px;
    }
    
    .export-card p {
        color: #DDDDDD;
        margin-bottom: 20px;
        font-size: 0.9rem;
    }
    
    .export-btn {
        background: #28a745;
        color: white;
        padding: 12px 25px;
        text-decoration: none;
        border-radius: 5px;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-block;
    }
    
    .export-btn:hover {
        background: #218838;
        transform: translateY(-2px);
        box-shadow: 0px 0px 9px 3px #FFFBC4;
        text-decoration: none;
    }
    
    .stats-info {
        background: #333333;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
        color: #DDDDDD;
        font-size: 0.9rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007BFF;
    }
    
    @media (max-width: 768px) {
        .admin-nav {
            flex-direction: column;
        }
        
        .admin-nav a {
            text-align: center;
        }
        
        .export-options {
            grid-template-columns: 1fr;
        }
    }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>DINCO E-shop - Administrace</h1>
            <div class="admin-nav">
                <a href="dashboard.asp">Dashboard</a>
                <a href="products.asp">Produkty</a>
                <a href="categories.asp">Kategorie</a>
                <a href="orders.asp">Objednávky</a>
                <a href="import.asp">Import dat</a>
                <a href="../default.asp">Zpět na e-shop</a>
            </div>
        </div>

        <div class="export-section">
            <h2>Export dat do CSV</h2>
            <p style="color: #DDDDDD; margin-bottom: 30px;">
                Exportujte data z e-shopu do CSV souborů pro zálohu nebo další zpracování.
            </p>
            
            <div class="export-options">
                <!-- Export kategorií -->
                <div class="export-card">
                    <h3>Kategorie</h3>
                    <div class="stats-info">
                        <div class="stats-number"><%=GetSingleValue("SELECT COUNT(*) FROM Categories")%></div>
                        kategorií v databázi
                    </div>
                    <p>Export všech kategorií včetně názvů, popisů a SEO URL.</p>
                    <a href="export.asp?type=categories" class="export-btn">Exportovat kategorie</a>
                </div>
                
                <!-- Export produktů -->
                <div class="export-card">
                    <h3>Produkty</h3>
                    <div class="stats-info">
                        <div class="stats-number"><%=GetSingleValue("SELECT COUNT(*) FROM Products")%></div>
                        produktů v databázi
                    </div>
                    <p>Export všech produktů včetně cen, skladových zásob a kategorií.</p>
                    <a href="export.asp?type=products" class="export-btn">Exportovat produkty</a>
                </div>
                
                <!-- Export objednávek -->
                <div class="export-card">
                    <h3>Objednávky</h3>
                    <div class="stats-info">
                        <div class="stats-number"><%=GetSingleValue("SELECT COUNT(*) FROM Orders")%></div>
                        objednávek v databázi
                    </div>
                    <p>Export všech objednávek včetně zákaznických údajů a stavů.</p>
                    <a href="export.asp?type=orders" class="export-btn">Exportovat objednávky</a>
                </div>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #333333; border-radius: 5px;">
                <h4 style="color: #FFFFFF; margin-bottom: 15px;">Informace o exportu</h4>
                <ul style="color: #DDDDDD; margin: 0; padding-left: 20px;">
                    <li>Soubory jsou exportovány ve formátu CSV s kódováním UTF-8</li>
                    <li>Oddělování polí je provedeno středníkem (;)</li>
                    <li>Textová pole jsou uzavřena v uvozovkách</li>
                    <li>Export obsahuje všechna data včetně neaktivních záznamů</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
