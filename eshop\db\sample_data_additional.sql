-- Vložen<PERSON> nových kategorií pro vruty
INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
SELECT N'Vruty', N'Vruty různých typů', CategoryID, 'vruty', 1, 2
FROM Categories WHERE Name = N'Nerezový spojovací materiál';

DECLARE @VrutyID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES (N'Vruty do dřeva', N'Vruty určené do dřeva', @VrutyID, 'vruty-do-dreva', 1, 1);

DECLARE @VrutyDoDrevaID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES (N'DIN 7997', N'Vruty dle normy DIN 7997', @VrutyDoDrevaID, 'din-7997', 1, 1);

DECLARE @DIN7997ID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES (N'A 2', N'Vruty v provedení A2', @DIN7997ID, 'a2', 1, 1);

DECLARE @DIN7997A2ID INT = SCOPE_IDENTITY();

-- Vložení nových kategorií pro podložky
INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
SELECT N'Podložky', N'Podložky různých typů', CategoryID, 'podlozky', 1, 3
FROM Categories WHERE Name = N'Nerezový spojovací materiál';

DECLARE @PodlozkyID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES (N'Ploché', N'Ploché podložky', @PodlozkyID, 'ploche', 1, 1);

DECLARE @PlocheID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES (N'DIN 9021', N'Podložky dle normy DIN 9021', @PlocheID, 'din-9021', 1, 1);

DECLARE @DIN9021ID INT = SCOPE_IDENTITY();

INSERT INTO Categories (Name, Description, ParentCategoryID, SEOUrl, IsActive, OrderIndex)
VALUES (N'A 2', N'Podložky v provedení A2', @DIN9021ID, 'a2', 1, 1);

DECLARE @DIN9021A2ID INT = SCOPE_IDENTITY();

-- Vložení vrutů
INSERT INTO Products (SKU, Name, Description, Price, Stock, CategoryID, SEOUrl, IsActive)
VALUES 
(N'**********', N'vrut 5 x 40 , ( záp.hl.+kř.dr. ) DIN7997/A2', N'Nerezový vrut do dřeva s křížovou hlavou', 3.50, 100, @DIN7997A2ID, 'vrut-5x40-din7997-a2', 1),
(N'**********', N'vrut 5 x 45 , ( záp.hl.+kř.dr. ) DIN7997/A2', N'Nerezový vrut do dřeva s křížovou hlavou', 3.70, 100, @DIN7997A2ID, 'vrut-5x45-din7997-a2', 1),
(N'**********', N'vrut 5 x 50 , ( záp.hl.+kř.dr. ) DIN7997/A2', N'Nerezový vrut do dřeva s křížovou hlavou', 3.90, 100, @DIN7997A2ID, 'vrut-5x50-din7997-a2', 1),
(N'1170000101', N'vrut 5 x 60 , ( záp.hl.+kř.dr. ) DIN7997/A2', N'Nerezový vrut do dřeva s křížovou hlavou', 4.10, 100, @DIN7997A2ID, 'vrut-5x60-din7997-a2', 1),
(N'**********', N'vrut 5 x 70 , ( záp.hl.+kř.dr. ) DIN7997/A2', N'Nerezový vrut do dřeva s křížovou hlavou', 4.30, 100, @DIN7997A2ID, 'vrut-5x70-din7997-a2', 1);

-- Vložení podložek
INSERT INTO Products (SKU, Name, Description, Price, Stock, CategoryID, SEOUrl, IsActive)
VALUES 
(N'M610000101', N'podložka 8.4 , ( velká 3d ) , DIN 9021/A2', N'Nerezová podložka velká', 2.50, 100, @DIN9021A2ID, 'podlozka-8-4-din9021-a2', 1),
(N'**********', N'podložka 10.5 , ( velká 3d ) , DIN 9021/A2', N'Nerezová podložka velká', 2.70, 100, @DIN9021A2ID, 'podlozka-10-5-din9021-a2', 1),
(N'N610000101', N'podložka 13 , ( velká 3d ) , DIN 9021/A2', N'Nerezová podložka velká', 2.90, 100, @DIN9021A2ID, 'podlozka-13-din9021-a2', 1),
(N'E340000101', N'podložka 15 , ( velká 3d ) , DIN 9021/A2', N'Nerezová podložka velká', 3.10, 100, @DIN9021A2ID, 'podlozka-15-din9021-a2', 1),
(N'U930000101', N'podložka 17 , ( velká 3d ) , DIN 9021/A2', N'Nerezová podložka velká', 3.30, 100, @DIN9021A2ID, 'podlozka-17-din9021-a2', 1);

-- Vložení kódů produktů
INSERT INTO ProductParameters (ProductID, Name, Value)
SELECT 
    ProductID,
    N'CODE',
    CASE 
        WHEN SKU = '**********' THEN N'10799700050040'
        WHEN SKU = '**********' THEN N'10799700050045'
        WHEN SKU = '**********' THEN N'10799700050050'
        WHEN SKU = '1170000101' THEN N'10799700050060'
        WHEN SKU = '**********' THEN N'10799700050070'
        WHEN SKU = 'M610000101' THEN N'10902100080000'
        WHEN SKU = '**********' THEN N'10902100100000'
        WHEN SKU = 'N610000101' THEN N'10902100120000'
        WHEN SKU = 'E340000101' THEN N'10902100140000'
        WHEN SKU = 'U930000101' THEN N'10902100160000'
    END
FROM Products
WHERE SKU IN (
    '**********', '**********', '**********', '1170000101', '**********',
    'M610000101', '**********', 'N610000101', 'E340000101', 'U930000101'
);