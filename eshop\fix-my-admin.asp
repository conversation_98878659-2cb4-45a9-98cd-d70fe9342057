<%@ CodePage=65001 %>
<!--#include file="includes/config.asp"-->
<!--#include file="includes/database.asp"-->
<%
Response.CharSet = "utf-8"
Response.CodePage = 65001

Dim message, success
success = False

' Pokud je už<PERSON>, pov<PERSON><PERSON><PERSON><PERSON> ho na admina
If Session("UserID") <> "" Then
    ' Nastavíme admin práva v databázi
    ExecuteSQL "UPDATE Users SET IsAdmin = 1 WHERE UserID = " & SafeSQL(Session("UserID"))
    
    ' Nastavíme admin práva v session
    Session("IsAdmin") = "1"
    
    message = "Úspěch! Byli jste povýšeni na administrátora."
    success = True
Else
    message = "Chyba: Nejste přihlášeni."
End If
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Oprava admin práv - DINCO</title>
    <style>
    body {
        font-family: Arial, sans-serif;
        background-color: #1A1A1A;
        color: #DDDDDD;
        padding: 20px;
        text-align: center;
    }
    
    .container {
        max-width: 500px;
        margin: 50px auto;
        background-color: #2A2A2A;
        padding: 40px;
        border-radius: 8px;
        border: 2px solid #292d31;
    }
    
    h1 {
        color: #FFFFFF;
        margin-bottom: 30px;
    }
    
    .message {
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
        font-size: 18px;
        font-weight: bold;
    }
    
    .success {
        background-color: #28a745;
        color: white;
    }
    
    .error {
        background-color: #dc3545;
        color: white;
    }
    
    .btn {
        background-color: #007BFF;
        color: white;
        padding: 15px 30px;
        text-decoration: none;
        border-radius: 5px;
        display: inline-block;
        margin: 10px;
        font-weight: 600;
        font-size: 16px;
        transition: background-color 0.3s ease;
    }
    
    .btn:hover {
        background-color: #0056b3;
        text-decoration: none;
    }
    
    .btn.success {
        background-color: #28a745;
    }
    
    .btn.success:hover {
        background-color: #218838;
    }
    
    .info {
        background-color: #333333;
        padding: 20px;
        border-radius: 5px;
        margin: 20px 0;
    }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Oprava admin práv</h1>
        
        <div class="message <% If success Then %>success<% Else %>error<% End If %>">
            <%=message%>
        </div>
        
        <% If success Then %>
            <div class="info">
                <p><strong>Co dělat dále:</strong></p>
                <p>1. Klikněte na "Přejít do administrace"</p>
                <p>2. Nebo se odhlaste a přihlaste znovu</p>
            </div>
            
            <a href="admin/dashboard.asp" class="btn success">🚀 Přejít do administrace</a>
            <a href="logout.asp" class="btn">🔄 Odhlásit a přihlásit znovu</a>
        <% Else %>
            <div class="info">
                <p>Nejdříve se musíte přihlásit, pak znovu spusťte tento script.</p>
            </div>
            
            <a href="login.asp" class="btn">🔑 Přihlásit se</a>
        <% End If %>
        
        <div style="margin-top: 30px;">
            <a href="default.asp" class="btn">🏠 Zpět na e-shop</a>
        </div>
    </div>
</body>
</html>
